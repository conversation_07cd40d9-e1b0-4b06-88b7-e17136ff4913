<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProductService;
use App\Models\WarehouseProduct;
use App\Models\Warehouse;
use App\Models\User;
use App\Models\ProductServiceCategory;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Cashier2InventoryController extends Controller
{
    /**
     * عرض صفحة إدارة المخزون لمستخدمي Cashier2
     */
    public function index()
    {
        // التحقق من صلاحية Cashier2
        if (!Auth::user()->hasRole('Cashier2')) {
            return redirect()->back()->with('error', __('ليس لديك صلاحية للوصول إلى هذه الصفحة'));
        }

        try {
            // الحصول على المستودع الافتراضي أو الأول المتاح
            $warehouse = Warehouse::where('created_by', Auth::user()->creatorId())->first();
            
            if (!$warehouse) {
                return redirect()->back()->with('error', __('لا يوجد مستودع متاح'));
            }

            // الحصول على جميع المنتجات مع معلومات المخزون
            $products = ProductService::where('created_by', Auth::user()->creatorId())
                ->with(['category', 'unit'])
                ->leftJoin('warehouse_products', function($join) use ($warehouse) {
                    $join->on('product_services.id', '=', 'warehouse_products.product_id')
                         ->where('warehouse_products.warehouse_id', '=', $warehouse->id);
                })
                ->select(
                    'product_services.*',
                    'warehouse_products.quantity as warehouse_quantity'
                )
                ->orderBy('product_services.name')
                ->get();

            return view('company_operations.inventory_management.cashier2_index', compact('products', 'warehouse'));

        } catch (\Exception $e) {
            Log::error('Cashier2 Inventory Index Error: ' . $e->getMessage());
            return redirect()->back()->with('error', __('حدث خطأ أثناء تحميل البيانات'));
        }
    }

    /**
     * تحديث كمية المنتج في المخزون (Cashier2 فقط)
     */
    public function updateQuantity(Request $request)
    {
        // التحقق من صلاحية Cashier2
        if (!Auth::user()->hasRole('Cashier2')) {
            return response()->json([
                'success' => false,
                'message' => __('ليس لديك صلاحية لتنفيذ هذا الإجراء')
            ], 403);
        }

        $request->validate([
            'product_id' => 'required|exists:product_services,id',
            'current_quantity' => 'required|numeric|min:0',
            'reason' => 'nullable|string|max:500'
        ]);

        try {
            DB::beginTransaction();

            $product = ProductService::findOrFail($request->product_id);
            
            // التحقق من أن المنتج ينتمي لنفس الشركة
            if ($product->created_by != Auth::user()->creatorId()) {
                return response()->json([
                    'success' => false,
                    'message' => __('غير مسموح بتعديل هذا المنتج')
                ], 403);
            }

            // الحصول على المستودع الافتراضي
            $warehouse = Warehouse::where('created_by', Auth::user()->creatorId())->first();
            
            if (!$warehouse) {
                return response()->json([
                    'success' => false,
                    'message' => __('لا يوجد مستودع متاح')
                ], 404);
            }

            // البحث عن سجل المنتج في المستودع أو إنشاؤه
            $warehouseProduct = WarehouseProduct::firstOrCreate(
                [
                    'warehouse_id' => $warehouse->id,
                    'product_id' => $product->id,
                    'created_by' => Auth::user()->creatorId()
                ],
                [
                    'quantity' => 0
                ]
            );

            $oldQuantity = $warehouseProduct->quantity;
            $newQuantity = $request->current_quantity;

            // تحديث الكمية
            $warehouseProduct->quantity = $newQuantity;
            $warehouseProduct->save();

            // تسجيل العملية في سجل التغييرات (إذا كان موجود)
            $this->logQuantityChange($product, $warehouse, $oldQuantity, $newQuantity, $request->reason);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('تم تحديث الكمية بنجاح'),
                'data' => [
                    'product_id' => $product->id,
                    'old_quantity' => $oldQuantity,
                    'new_quantity' => $newQuantity,
                    'warehouse_id' => $warehouse->id
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Cashier2 Update Quantity Error: ' . $e->getMessage(), [
                'product_id' => $request->product_id,
                'user_id' => Auth::id(),
                'error' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحديث الكمية')
            ], 500);
        }
    }

    /**
     * تسجيل تغيير الكمية في سجل العمليات
     */
    private function logQuantityChange($product, $warehouse, $oldQuantity, $newQuantity, $reason = null)
    {
        try {
            // يمكن إضافة جدول خاص لتسجيل تغييرات المخزون هنا
            Log::info('Cashier2 Quantity Change', [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'warehouse_id' => $warehouse->id,
                'warehouse_name' => $warehouse->name,
                'old_quantity' => $oldQuantity,
                'new_quantity' => $newQuantity,
                'difference' => $newQuantity - $oldQuantity,
                'reason' => $reason,
                'timestamp' => now()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log quantity change: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل منتج معين
     */
    public function showProduct($id)
    {
        // التحقق من صلاحية Cashier2
        if (!Auth::user()->hasRole('Cashier2')) {
            return response()->json([
                'success' => false,
                'message' => __('ليس لديك صلاحية للوصول إلى هذه البيانات')
            ], 403);
        }

        try {
            $product = ProductService::with(['category', 'unit'])
                ->where('id', $id)
                ->where('created_by', Auth::user()->creatorId())
                ->first();

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => __('المنتج غير موجود')
                ], 404);
            }

            // الحصول على كمية المخزون
            $warehouse = Warehouse::where('created_by', Auth::user()->creatorId())->first();
            $warehouseProduct = null;
            
            if ($warehouse) {
                $warehouseProduct = WarehouseProduct::where('warehouse_id', $warehouse->id)
                    ->where('product_id', $product->id)
                    ->first();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'product' => $product,
                    'warehouse_quantity' => $warehouseProduct ? $warehouseProduct->quantity : 0,
                    'warehouse' => $warehouse
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Cashier2 Show Product Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء تحميل بيانات المنتج')
            ], 500);
        }
    }

    /**
     * البحث في المنتجات
     */
    public function searchProducts(Request $request)
    {
        // التحقق من صلاحية Cashier2
        if (!Auth::user()->hasRole('Cashier2')) {
            return response()->json([
                'success' => false,
                'message' => __('ليس لديك صلاحية للوصول إلى هذه البيانات')
            ], 403);
        }

        try {
            $search = $request->get('search', '');
            $warehouse = Warehouse::where('created_by', Auth::user()->creatorId())->first();

            $query = ProductService::where('created_by', Auth::user()->creatorId())
                ->with(['category', 'unit']);

            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('sku', 'LIKE', "%{$search}%");
                });
            }

            if ($warehouse) {
                $query->leftJoin('warehouse_products', function($join) use ($warehouse) {
                    $join->on('product_services.id', '=', 'warehouse_products.product_id')
                         ->where('warehouse_products.warehouse_id', '=', $warehouse->id);
                })
                ->select(
                    'product_services.*',
                    'warehouse_products.quantity as warehouse_quantity'
                );
            }

            $products = $query->orderBy('product_services.name')->get();

            return response()->json([
                'success' => true,
                'data' => $products
            ]);

        } catch (\Exception $e) {
            Log::error('Cashier2 Search Products Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => __('حدث خطأ أثناء البحث')
            ], 500);
        }
    }

    /**
     * عرض صفحة إدارة المنتجات والخدمات لمستخدمي Cashier2
     */
    public function financialProductServiceIndex()
    {
        // التحقق من صلاحية Cashier2
        if (!Auth::user()->hasRole('Cashier2')) {
            return redirect()->back()->with('error', __('ليس لديك صلاحية للوصول إلى هذه الصفحة'));
        }

        try {
            // الحصول على المستودع الافتراضي أو الأول المتاح
            $warehouse = Warehouse::where('created_by', Auth::user()->creatorId())->first();

            // الحصول على جميع الفئات
            $categories = ProductServiceCategory::where('created_by', Auth::user()->creatorId())
                ->orderBy('name')
                ->get();

            // الحصول على جميع المنتجات والخدمات مع معلومات المخزون
            $products = ProductService::where('created_by', Auth::user()->creatorId())
                ->with(['category', 'unit']);

            if ($warehouse) {
                $products = $products->leftJoin('warehouse_products', function($join) use ($warehouse) {
                    $join->on('product_services.id', '=', 'warehouse_products.product_id')
                         ->where('warehouse_products.warehouse_id', '=', $warehouse->id);
                })
                ->select(
                    'product_services.*',
                    'warehouse_products.quantity as warehouse_quantity'
                );
            }

            $products = $products->orderBy('product_services.name')->get();

            return view('company_operations.financial_product_service.cashier2_index', compact('products', 'warehouse', 'categories'));

        } catch (\Exception $e) {
            Log::error('Cashier2 Financial Product Service Index Error: ' . $e->getMessage());
            return redirect()->back()->with('error', __('حدث خطأ أثناء تحميل البيانات'));
        }
    }
}
