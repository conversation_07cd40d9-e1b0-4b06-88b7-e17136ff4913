<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckCashier2Role
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        if (!Auth::user()->hasRole('Cashier2')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => __('ليس لديك صلاحية للوصول إلى هذه الصفحة')
                ], 403);
            }
            
            return redirect()->back()->with('error', __('ليس لديك صلاحية للوصول إلى هذه الصفحة'));
        }

        return $next($request);
    }
}
