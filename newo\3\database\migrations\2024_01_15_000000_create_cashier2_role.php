<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إنشاء دور Cashier2
        $role = Role::firstOrCreate(['name' => 'Cashier2']);

        // إنشاء الصلاحيات الخاصة بـ Cashier2
        $permissions = [
            'manage inventory quantity',
            'view financial products',
            'view inventory management',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // ربط الصلاحيات بالدور
        $role->syncPermissions($permissions);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // حذف الدور والصلاحيات
        $role = Role::where('name', 'Cashier2')->first();
        if ($role) {
            $role->delete();
        }

        $permissions = [
            'manage inventory quantity',
            'view financial products', 
            'view inventory management',
        ];

        foreach ($permissions as $permission) {
            $perm = Permission::where('name', $permission)->first();
            if ($perm) {
                $perm->delete();
            }
        }
    }
};
