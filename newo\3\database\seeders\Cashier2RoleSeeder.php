<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class Cashier2RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء دور Cashier2
        $cashier2Role = Role::firstOrCreate([
            'name' => 'Cashier2',
            'guard_name' => 'web'
        ]);

        // إنشاء الصلاحيات الخاصة بـ Cashier2
        $permissions = [
            // صلاحيات إدارة المخزون
            'manage inventory quantity' => 'إدارة كمية المخزون',
            'view inventory management' => 'عرض إدارة المخزون',
            
            // صلاحيات المنتجات المالية
            'view financial products' => 'عرض المنتجات المالية',
            'manage financial products' => 'إدارة المنتجات المالية',
            
            // صلاحيات أساسية
            'show dashboard' => 'عرض لوحة التحكم',
        ];

        foreach ($permissions as $permission => $description) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web'
            ]);
        }

        // ربط الصلاحيات بدور Cashier2
        $cashier2Role->syncPermissions(array_keys($permissions));

        $this->command->info('تم إنشاء دور Cashier2 والصلاحيات المرتبطة به بنجاح');
    }
}
