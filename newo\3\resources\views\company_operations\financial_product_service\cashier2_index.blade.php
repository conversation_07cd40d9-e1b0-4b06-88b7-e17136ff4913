@extends('layouts.admin')

@section('page-title')
    {{ __('إدارة المنتجات والخدمات - Cashier2') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Home') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة المنتجات والخدمات') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <button type="button" class="btn btn-sm btn-primary" onclick="refreshTable()">
            <i class="ti ti-refresh"></i> {{ __('تحديث') }}
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <h5>{{ __('إدارة المنتجات والخدمات') }}</h5>
                    <small class="text-muted">{{ __('عرض المنتجات والخدمات المتاحة') }}</small>
                </div>
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="search_product">{{ __('البحث في المنتجات') }}</label>
                                <input type="text" class="form-control" id="search_product" placeholder="{{ __('اسم المنتج أو الرمز التعريفي') }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter_category">{{ __('الفئة') }}</label>
                                <select class="form-control" id="filter_category">
                                    <option value="">{{ __('جميع الفئات') }}</option>
                                    @if(isset($categories))
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="filter_type">{{ __('النوع') }}</label>
                                <select class="form-control" id="filter_type">
                                    <option value="">{{ __('الكل') }}</option>
                                    <option value="product">{{ __('منتج') }}</option>
                                    <option value="service">{{ __('خدمة') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-primary d-block w-100" onclick="applyFilters()">
                                    <i class="ti ti-search"></i> {{ __('بحث') }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table datatable" id="products-table">
                            <thead>
                                <tr>
                                    <th>{{ __('الصورة') }}</th>
                                    <th>{{ __('المنتج/الخدمة') }}</th>
                                    <th>{{ __('الرمز التعريفي') }}</th>
                                    <th>{{ __('النوع') }}</th>
                                    <th>{{ __('الفئة') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('سعر الشراء') }}</th>
                                    <th>{{ __('الضريبة') }}</th>
                                    <th>{{ __('الوحدة') }}</th>
                                    <th>{{ __('الكمية المتاحة') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($products) && count($products) > 0)
                                    @foreach ($products as $product)
                                        <tr class="font-style">
                                            <td class="text-center">
                                                <div class="product-image-container">
                                                    @if(!empty($product->pro_image))
                                                        <img src="{{ \App\Models\Utility::get_file('uploads/pro_image/'.$product->pro_image) }}"
                                                             alt="{{ $product->name }}"
                                                             class="product-image rounded shadow-sm clickable-image"
                                                             title="{{ __('انقر لتكبير الصورة') }}"
                                                             data-bs-toggle="modal"
                                                             data-bs-target="#imageModal"
                                                             data-image-src="{{ \App\Models\Utility::get_file('uploads/pro_image/'.$product->pro_image) }}"
                                                             data-product-name="{{ $product->name }}"
                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                        <div class="default-product-image rounded shadow-sm d-flex align-items-center justify-content-center" style="display: none;">
                                                            <span class="text-muted fw-bold">{{ strtoupper(substr($product->name, 0, 2)) }}</span>
                                                        </div>
                                                    @else
                                                        <div class="default-product-image rounded shadow-sm d-flex align-items-center justify-content-center" title="{{ $product->name }}">
                                                            <span class="text-muted fw-bold">{{ strtoupper(substr($product->name, 0, 2)) }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="product-info">
                                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                                    @if($product->description)
                                                        <small class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">{{ $product->sku }}</span>
                                            </td>
                                            <td>
                                                @if($product->type == 'product')
                                                    <span class="badge badge-primary">{{ __('منتج') }}</span>
                                                @else
                                                    <span class="badge badge-info">{{ __('خدمة') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->category)
                                                    <span class="badge badge-info">{{ $product->category->name }}</span>
                                                @else
                                                    <span class="text-muted">{{ __('غير محدد') }}</span>
                                                @endif
                                            </td>
                                            <td>{{ Auth::user()->priceFormat($product->sale_price) }}</td>
                                            <td>{{ Auth::user()->priceFormat($product->purchase_price) }}</td>
                                            <td>
                                                @if($product->tax_id)
                                                    @php
                                                        $taxes = \App\Models\Utility::tax($product->tax_id);
                                                    @endphp
                                                    @foreach($taxes as $tax)
                                                        <span class="badge badge-primary">{{ $tax->name }} ({{ $tax->rate }}%)</span>
                                                    @endforeach
                                                @else
                                                    <span class="text-muted">{{ __('بدون ضريبة') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->unit)
                                                    <span class="badge badge-secondary">{{ $product->unit->name }}</span>
                                                @else
                                                    <span class="text-muted">{{ __('غير محدد') }}</span>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($product->type == 'product')
                                                    @php
                                                        $quantity = $product->warehouse_quantity ?? 0;
                                                        $isLowStock = isset($product->min_quantity) && $quantity < $product->min_quantity;
                                                    @endphp
                                                    <span class="badge {{ $isLowStock ? 'badge-danger' : ($quantity > 0 ? 'badge-success' : 'badge-warning') }}">
                                                        {{ $quantity }}
                                                    </span>
                                                @else
                                                    <span class="text-muted">{{ __('غير محدود') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->type == 'product')
                                                    @php
                                                        $quantity = $product->warehouse_quantity ?? 0;
                                                        $isLowStock = isset($product->min_quantity) && $quantity < $product->min_quantity;
                                                    @endphp
                                                    @if($isLowStock)
                                                        <span class="badge badge-danger">{{ __('مخزون منخفض') }}</span>
                                                    @elseif($quantity > 0)
                                                        <span class="badge badge-success">{{ __('متوفر') }}</span>
                                                    @else
                                                        <span class="badge badge-warning">{{ __('نفد المخزون') }}</span>
                                                    @endif
                                                @else
                                                    <span class="badge badge-success">{{ __('متاح') }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="11" class="text-center text-muted py-4">
                                            <i class="ti ti-package-off fs-1 mb-3 d-block"></i>
                                            {{ __('لا توجد منتجات متاحة') }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لعرض الصورة المكبرة -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">{{ __('صورة المنتج') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="" class="img-fluid rounded shadow">
                    <p id="modalProductName" class="mt-3 text-muted"></p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // تهيئة DataTable
        if ($.fn.dataTable.isDataTable('#products-table')) {
            $('#products-table').DataTable().destroy();
        }
        
        $('#products-table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "order": [[ 1, "asc" ]],
            "pageLength": 25,
            "responsive": true
        });

        // تفعيل Modal الصورة
        $(document).on('click', '.clickable-image', function() {
            var imageSrc = $(this).data('image-src');
            var productName = $(this).data('product-name');

            $('#modalImage').attr('src', imageSrc);
            $('#modalImage').attr('alt', productName);
            $('#modalProductName').text(productName);
        });
    });

    function refreshTable() {
        location.reload();
    }

    function applyFilters() {
        var search = $('#search_product').val();
        var category = $('#filter_category').val();
        var type = $('#filter_type').val();
        
        // تطبيق الفلاتر على DataTable
        var table = $('#products-table').DataTable();
        
        // البحث العام
        table.search(search).draw();
        
        // فلتر الفئة
        if (category) {
            table.column(4).search(category).draw();
        } else {
            table.column(4).search('').draw();
        }
        
        // فلتر النوع
        if (type) {
            table.column(3).search(type).draw();
        } else {
            table.column(3).search('').draw();
        }
    }
</script>

<style>
.product-image-container {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
}

.product-image:hover {
    transform: scale(1.1);
}

.default-product-image {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
}

.product-info h6 {
    font-weight: 600;
    color: #2c3e50;
}
</style>
@endpush
