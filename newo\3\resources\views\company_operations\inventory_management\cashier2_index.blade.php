@extends('layouts.admin')

@section('page-title')
    {{ __('إدارة المخزون ومراقبة المخزون - Cashier2') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Home') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة المخزون ومراقبة المخزون') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <button type="button" class="btn btn-sm btn-primary" onclick="refreshTable()">
            <i class="ti ti-refresh"></i> {{ __('تحديث') }}
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <h5>{{ __('إدارة المخزون ومراقبة المخزون') }}</h5>
                    <small class="text-muted">{{ __('يمكنك تعديل الكمية الحالية فقط') }}</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>{{ __('الصورة') }}</th>
                                    <th>{{ __('المنتج') }}</th>
                                    <th>{{ __('الرمز التعريفي') }}</th>
                                    <th>{{ __('سعر البيع') }}</th>
                                    <th>{{ __('الضريبة') }}</th>
                                    <th class="text-center">{{ __('الكمية الحالية') }}</th>
                                    <th class="text-center">{{ __('الحد الأدنى للكمية') }}</th>
                                    <th>{{ __('الفئة') }}</th>
                                    <th>{{ __('الوحدة') }}</th>
                                    <th>{{ __('الحالة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($products) && count($products) > 0)
                                    @foreach ($products as $product)
                                        @php
                                            $isLowStock = isset($product->min_quantity) && $product->warehouse_quantity < $product->min_quantity;
                                        @endphp
                                        <tr class="font-style {{ $isLowStock ? 'low-stock' : '' }}">
                                            <td class="text-center">
                                                <div class="product-image-container">
                                                    @if(!empty($product->pro_image))
                                                        <img src="{{ \App\Models\Utility::get_file('uploads/pro_image/'.$product->pro_image) }}"
                                                             alt="{{ $product->name }}"
                                                             class="product-image rounded shadow-sm clickable-image"
                                                             title="{{ __('انقر لتكبير الصورة') }}"
                                                             data-bs-toggle="modal"
                                                             data-bs-target="#imageModal"
                                                             data-image-src="{{ \App\Models\Utility::get_file('uploads/pro_image/'.$product->pro_image) }}"
                                                             data-product-name="{{ $product->name }}"
                                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                        <div class="default-product-image rounded shadow-sm d-flex align-items-center justify-content-center" style="display: none;">
                                                            <span class="text-muted fw-bold">{{ strtoupper(substr($product->name, 0, 2)) }}</span>
                                                        </div>
                                                    @else
                                                        <div class="default-product-image rounded shadow-sm d-flex align-items-center justify-content-center" title="{{ $product->name }}">
                                                            <span class="text-muted fw-bold">{{ strtoupper(substr($product->name, 0, 2)) }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <div class="product-info">
                                                    <h6 class="mb-1">{{ $product->name }}</h6>
                                                    @if($product->description)
                                                        <small class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">{{ $product->sku }}</span>
                                            </td>
                                            <td>{{ Auth::user()->priceFormat($product->sale_price) }}</td>
                                            <td>
                                                @if($product->tax_id)
                                                    @php
                                                        $taxes = \App\Models\Utility::tax($product->tax_id);
                                                    @endphp
                                                    @foreach($taxes as $tax)
                                                        <span class="badge badge-primary">{{ $tax->name }} ({{ $tax->rate }}%)</span>
                                                    @endforeach
                                                @else
                                                    <span class="text-muted">{{ __('بدون ضريبة') }}</span>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                <div class="quantity-edit-container">
                                                    <span class="quantity-display {{ $isLowStock ? 'text-danger fw-bold' : '' }}">
                                                        {{ $product->warehouse_quantity ?? 0 }}
                                                    </span>
                                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2 edit-quantity-btn" 
                                                            data-product-id="{{ $product->id }}" 
                                                            data-current-quantity="{{ $product->warehouse_quantity ?? 0 }}"
                                                            title="{{ __('تعديل الكمية') }}">
                                                        <i class="ti ti-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge {{ $isLowStock ? 'badge-danger' : 'badge-info' }}">
                                                    {{ $product->min_quantity ?? 0 }}
                                                </span>
                                            </td>
                                            <td>
                                                @if($product->category)
                                                    <span class="badge badge-info">{{ $product->category->name }}</span>
                                                @else
                                                    <span class="text-muted">{{ __('غير محدد') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($product->unit)
                                                    <span class="badge badge-secondary">{{ $product->unit->name }}</span>
                                                @else
                                                    <span class="text-muted">{{ __('غير محدد') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($isLowStock)
                                                    <span class="badge badge-danger">{{ __('مخزون منخفض') }}</span>
                                                @elseif($product->warehouse_quantity > 0)
                                                    <span class="badge badge-success">{{ __('متوفر') }}</span>
                                                @else
                                                    <span class="badge badge-warning">{{ __('نفد المخزون') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary edit-quantity-btn" 
                                                        data-product-id="{{ $product->id }}" 
                                                        data-current-quantity="{{ $product->warehouse_quantity ?? 0 }}"
                                                        title="{{ __('تعديل الكمية') }}">
                                                    <i class="ti ti-edit"></i> {{ __('تعديل الكمية') }}
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="11" class="text-center text-muted py-4">
                                            <i class="ti ti-package-off fs-1 mb-3 d-block"></i>
                                            {{ __('لا توجد منتجات متاحة') }}
                                        </td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal لتعديل الكمية -->
    <div class="modal fade" id="quantityEditModal" tabindex="-1" aria-labelledby="quantityEditModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quantityEditModalLabel">{{ __('تعديل الكمية الحالية') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="quantityEditForm">
                    <div class="modal-body">
                        <input type="hidden" id="edit_product_id" name="product_id">
                        <div class="mb-3">
                            <label for="current_quantity" class="form-label">{{ __('الكمية الحالية') }}</label>
                            <input type="number" class="form-control" id="current_quantity" name="current_quantity" min="0" step="1" required>
                            <div class="form-text">{{ __('أدخل الكمية الجديدة للمنتج') }}</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_reason" class="form-label">{{ __('سبب التعديل') }}</label>
                            <textarea class="form-control" id="edit_reason" name="reason" rows="3" placeholder="{{ __('اختياري - أدخل سبب تعديل الكمية') }}"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('إلغاء') }}</button>
                        <button type="submit" class="btn btn-primary">{{ __('حفظ التعديل') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal لعرض الصورة المكبرة -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">{{ __('صورة المنتج') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="" class="img-fluid rounded shadow">
                    <p id="modalProductName" class="mt-3 text-muted"></p>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // تهيئة DataTable
        if ($.fn.dataTable.isDataTable('.datatable')) {
            $('.datatable').DataTable().destroy();
        }
        
        $('.datatable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "order": [[ 1, "asc" ]],
            "pageLength": 25,
            "responsive": true
        });

        // تفعيل Modal الصورة
        $(document).on('click', '.clickable-image', function() {
            var imageSrc = $(this).data('image-src');
            var productName = $(this).data('product-name');

            $('#modalImage').attr('src', imageSrc);
            $('#modalImage').attr('alt', productName);
            $('#modalProductName').text(productName);
        });

        // تفعيل تعديل الكمية
        $(document).on('click', '.edit-quantity-btn', function() {
            var productId = $(this).data('product-id');
            var currentQuantity = $(this).data('current-quantity');
            
            $('#edit_product_id').val(productId);
            $('#current_quantity').val(currentQuantity);
            $('#edit_reason').val('');
            
            $('#quantityEditModal').modal('show');
        });

        // معالجة نموذج تعديل الكمية
        $('#quantityEditForm').on('submit', function(e) {
            e.preventDefault();
            
            var formData = {
                product_id: $('#edit_product_id').val(),
                current_quantity: $('#current_quantity').val(),
                reason: $('#edit_reason').val(),
                _token: '{{ csrf_token() }}'
            };

            $.ajax({
                url: '{{ route("inventory.management.update.quantity.cashier2") }}',
                method: 'POST',
                data: formData,
                success: function(response) {
                    if(response.success) {
                        show_toastr('Success', response.message, 'success');
                        $('#quantityEditModal').modal('hide');
                        location.reload(); // إعادة تحميل الصفحة لإظهار التحديثات
                    } else {
                        show_toastr('Error', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    var errorMessage = 'حدث خطأ أثناء تحديث الكمية';
                    if(xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    show_toastr('Error', errorMessage, 'error');
                }
            });
        });
    });

    function refreshTable() {
        location.reload();
    }
</script>

<style>
.product-image-container {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
}

.product-image:hover {
    transform: scale(1.1);
}

.default-product-image {
    width: 50px;
    height: 50px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
}

.low-stock {
    background-color: #fff5f5;
}

.quantity-edit-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-display {
    font-weight: 600;
    font-size: 1.1em;
}
</style>
@endpush
