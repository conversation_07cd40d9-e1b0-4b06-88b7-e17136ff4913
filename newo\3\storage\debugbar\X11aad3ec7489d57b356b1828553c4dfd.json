{"__meta": {"id": "X11aad3ec7489d57b356b1828553c4dfd", "datetime": "2025-06-13 08:58:17", "utime": **********.156617, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749805096.114326, "end": **********.156659, "duration": 1.0423328876495361, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1749805096.114326, "relative_start": 0, "end": **********.020767, "relative_end": **********.020767, "duration": 0.9064409732818604, "duration_str": "906ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.020786, "relative_start": 0.9064600467681885, "end": **********.156664, "relative_end": 5.0067901611328125e-06, "duration": 0.1358778476715088, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44184592, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.005940000000000001, "accumulated_duration_str": "5.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.092449, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.549}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.119957, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.549, "width_percent": 14.983}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1353152, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.532, "width_percent": 13.468}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1216283817 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1216283817\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1335601717 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1335601717\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-718215357 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-718215357\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-341464993 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804742596%7C7%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill5UldUbEVnMjdObzlhTm0yNlNqWGc9PSIsInZhbHVlIjoibWZKNkhEd1R2Z2JEeGhQOTdZWDJtbkhWTktwMGxZVkQ2RGVITUVpYWdEUUNqZGZ4WGZIc25EcTNkMGgxOU9ERlExZEdmckdHakNSbk9WUkhUM1RsUXUvQ3VVM281TEFoaTlWazVwQWdwM3V2MHl1bVhOeTQyN0psMEgvTkVrVXdJcm9HVklQUGRDQzZWVjU5YkdydnJxR0h2ZjI1bWUvNWk3TW8ra1JFUERxbUFTL24wUlIvL0ZBSWhTdTZ3NG01bEk0TVU2YmFCV1hJajJrN0IzK1ZCbmZYZDV6MUpVcmxxbUw4RWppUk9PMzVrckpSYmo3VzIrRkxMemowS2M0QXJoY1I0VllWYXp4UmhYN0lnUHRxKzVkVS8xQURSTDgzdDFyRk1JY3RMRkhZcEw2aUsvRWtMUUh5dFMwTzcvakkwT3RDL3k0bU5XV0ZXVWZrY3Y5NmdrUks5RUVsVnh6NGRxck1KZGNiRWNzM3ZCNjVRVEh4NEYwSUZXU3FVNnIwMit4RlZZeGRrQzBLMUVBQlJFTUQxaDAzK1gwcWlNZEg1MHVRK25TUDMxL3NFVHJoKy8wVS81Rm5OWVZuUlYyQTV5OHUzWUd4T2hSYTdENWdpN05mYUsxVUlwS0ZoQUZMME9IOGxTc2xSdmV2TUQ1c0swMmIzRXdFclZGR2Ztem8iLCJtYWMiOiIxZTQyYjU2N2M0NWMzNjAyNWRkMGI5MTFlZDA4NjRhMjUzYzg4MDU5NjQ0ZWQzMTQ3YzI1MDRlZmQ0MTIzNDNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjJxSGovVHBnNFhnUWdJL2FXek5SOVE9PSIsInZhbHVlIjoiTmliUjFnVWVEeXExdm1LaTRCeDNwZWI1N044cmZzdkVlTGVqNWlVdmNSYktpTW44NUlQKzkzSGFUbFRaSlRGZ2N4aWtRaE9VUldKUVBQUElRRGsyN211VlllSEtpak1oMUtDY0ovekNFTHE2dE42QTNkeWk3TTg1RFJEK1pQNnFvVTg1N3VZQXlIbHhWUjRoWklUcE00QUtwdzFsc3FmenpKM29ReDA5YkMwODBpSmdpTDJ4OExXUGZWQ0NhVmtUdFAwSkp5UHh2elRNTmhBeDhWV0c5WGpQSWZOeDY2dXhrck1HVUM3RVRGcjRUMFJkRldzM3FUN2ZHQmxLa280TjJuNEQ1ai82ZThCekEwNGVDbmxPUit2NjQ2eGJtOG1kNG5rT0xIT0xHYTRUV3Y3RzgwbDRNZlAzNm9hSWVQbXhWVkxPNEtKWW5EU0RVSkNOWkNrL0NUNzZnL3hnK3UrNTllVWRHRDQ1ZmJMMy9yVUZLSlhEVHZ5aWRibmMwN1IrTUFxZFpLamZUdEx0LzdQWmV5SDczUXUrK3owUXBKME14U3MwUjdweWF6T29qZWk1Zk44ZCs0YzdoRkFPRFh5bFVVTldtdzhxTEZqVTEzNWNYcS9xek5pZGw1a01JdXl1ZFQzSXZteTVuQW1WbGRLdHZvdkVvS2FMSEQvbjJwWXAiLCJtYWMiOiIyNDNiMzc2MjdiZDYzMzFiZWI4N2VmNDQzMGYxODVlMjZiMjY1OTgzMGVkYTUwM2ExYTVjNWYxNWNiZmMwMjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341464993\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1570372551 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570372551\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1109246441 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:58:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVzeVhsVy9DVUZzWlhtQ3NpRzBhc1E9PSIsInZhbHVlIjoiUktkdlZWbXJvNDlyV0xXaUwzaTBZZjdKMzlnYlZ2VzgxekxuQ3Q1Zk9mY2Nib3laOFBxSmM2QTZlZXlHZm9SN0QwT2habklVZlVvaU9oRzdYZkxZOUhwQm5zZzhHUFhSSUo5YXZzdGxiZEJFQ1JZWGR5NTJhV0NQenF3czdEUWNCdXNsdWk4Q3p1NE9MSllXbUFRUmFnTXFnM3FOQ0xtTDZVemRHTmc5eTNadUxUNjBSVzdQb0J4Ky9JR0hUWTZWSjd1QXJ2WjgvbHZqRGRUdUdwNVJ1VzFUeG9GMi9DMk9zQVJPMC9sQmZta3NEU1djUkdrZER3ZnZ4Ym85azlGaFM2SFp1Q3B1a1k5RWlHSGRrVDZaK2JJYnN3QVU5ZjN1c1g5dEZULzFjU3NpdHYrS0QvRTB0eW94VnE5TWY2MnR1UGdldzQ5UzVlamNBTWlkbFZxVUMyVldCbjc3ZjZ3TTQ4c2V0MzkzeWNMeW5oZXNWNFBzS0hCYzJWdnVtN0NCUUQ4YmwvY3A1QXNIQ0c4R1pkdW1LbUpCTUVMeGl1ckNPTkUvN3RrQXh6S2p4Yk5OUXZSeVV0ZUJ5d3RCdnZoRkhNd05jY3Y0NzZ0bUVKc29hY2hKVzIyVkpkWWRmUklreXd6eS9sVHF0bVhIOXp6RUdqM3dDRXo1UmoyWndyWlYiLCJtYWMiOiJmNGJlMTNlYjc0ZWY4ZmEyZWQ0NDg1MWZhZDgwZDdjNzJhNjAzNmI0OWM4ODJlZTAyMTEwOTQ0MTNmYTAzZDk5IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjIzM05idE8zMlFkbzA1QkZBRG9ZekE9PSIsInZhbHVlIjoiRURpcHcvanhhb0xMS05kSU1FeUFaeithZXBpa1lyaEFtWXc5Qjl6b05aTFFNYlNPWFdTVHprUFZvVXF6L1d1RW9HQ2E5dWVtYUVQc25vLzFsVGVzOGJyeFhXcW93RkRRSGxSQm1jOXMzME94eWIwR3J1RTAybnAyRnNyTmNNMEVJZU1NMmhVTUNzcXp1STZOVzFUYUZpMWVlSy9uSE8wcHNYTTF3YjI5T3l0Q3NHTGQ2WWJRMXJ2RUk3MmVCYUczL1QwZzZqMDFISDBlOFF0MURmY1BXbEVvZ09RVW1LY014SXBUdXpaRUhFaUQ3RDRWMDU0K2phaHJCNGxZRUcyWDlUUGpWRVlRNXlsWS9EREd1UVZlSW5uVWk2Y1hENnlBWVZxTHhiRFdTQVM5SWNlSFBPWmE4Q2toL2lueFdDMzNUbTdlbm1kck10Ny9GK09aOHNDcVphckx2c1R4a0V4VGFxRXp5L3IzK1dtUi9OV0ZqcHBKaGVmLzVBdzU0WTBUOHRIeWhmVGEwK3ZyZ3RuSjgzc2xzSGJySHlIWUs0YjUwMElpN3MzUUgzWml2bEwwV255QU1QUlRobVY2ZnhLVU8xbGZrdkFqMUp3aWZ1M2ZZSzlxbEt1Nnd6MkIwOVdtcHV2ci9rQ2pMd1lsNUU1THNIZjdoVGZHTERTMjBuV1kiLCJtYWMiOiI5NzJlOWI4ZDI4YTIxZDM0MDllYzE0NjBlZjAyM2I2NjIyM2M4YTZmNGI1NTNhMTI0YjQyMTdiMzc2NzNhY2ZlIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVzeVhsVy9DVUZzWlhtQ3NpRzBhc1E9PSIsInZhbHVlIjoiUktkdlZWbXJvNDlyV0xXaUwzaTBZZjdKMzlnYlZ2VzgxekxuQ3Q1Zk9mY2Nib3laOFBxSmM2QTZlZXlHZm9SN0QwT2habklVZlVvaU9oRzdYZkxZOUhwQm5zZzhHUFhSSUo5YXZzdGxiZEJFQ1JZWGR5NTJhV0NQenF3czdEUWNCdXNsdWk4Q3p1NE9MSllXbUFRUmFnTXFnM3FOQ0xtTDZVemRHTmc5eTNadUxUNjBSVzdQb0J4Ky9JR0hUWTZWSjd1QXJ2WjgvbHZqRGRUdUdwNVJ1VzFUeG9GMi9DMk9zQVJPMC9sQmZta3NEU1djUkdrZER3ZnZ4Ym85azlGaFM2SFp1Q3B1a1k5RWlHSGRrVDZaK2JJYnN3QVU5ZjN1c1g5dEZULzFjU3NpdHYrS0QvRTB0eW94VnE5TWY2MnR1UGdldzQ5UzVlamNBTWlkbFZxVUMyVldCbjc3ZjZ3TTQ4c2V0MzkzeWNMeW5oZXNWNFBzS0hCYzJWdnVtN0NCUUQ4YmwvY3A1QXNIQ0c4R1pkdW1LbUpCTUVMeGl1ckNPTkUvN3RrQXh6S2p4Yk5OUXZSeVV0ZUJ5d3RCdnZoRkhNd05jY3Y0NzZ0bUVKc29hY2hKVzIyVkpkWWRmUklreXd6eS9sVHF0bVhIOXp6RUdqM3dDRXo1UmoyWndyWlYiLCJtYWMiOiJmNGJlMTNlYjc0ZWY4ZmEyZWQ0NDg1MWZhZDgwZDdjNzJhNjAzNmI0OWM4ODJlZTAyMTEwOTQ0MTNmYTAzZDk5IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjIzM05idE8zMlFkbzA1QkZBRG9ZekE9PSIsInZhbHVlIjoiRURpcHcvanhhb0xMS05kSU1FeUFaeithZXBpa1lyaEFtWXc5Qjl6b05aTFFNYlNPWFdTVHprUFZvVXF6L1d1RW9HQ2E5dWVtYUVQc25vLzFsVGVzOGJyeFhXcW93RkRRSGxSQm1jOXMzME94eWIwR3J1RTAybnAyRnNyTmNNMEVJZU1NMmhVTUNzcXp1STZOVzFUYUZpMWVlSy9uSE8wcHNYTTF3YjI5T3l0Q3NHTGQ2WWJRMXJ2RUk3MmVCYUczL1QwZzZqMDFISDBlOFF0MURmY1BXbEVvZ09RVW1LY014SXBUdXpaRUhFaUQ3RDRWMDU0K2phaHJCNGxZRUcyWDlUUGpWRVlRNXlsWS9EREd1UVZlSW5uVWk2Y1hENnlBWVZxTHhiRFdTQVM5SWNlSFBPWmE4Q2toL2lueFdDMzNUbTdlbm1kck10Ny9GK09aOHNDcVphckx2c1R4a0V4VGFxRXp5L3IzK1dtUi9OV0ZqcHBKaGVmLzVBdzU0WTBUOHRIeWhmVGEwK3ZyZ3RuSjgzc2xzSGJySHlIWUs0YjUwMElpN3MzUUgzWml2bEwwV255QU1QUlRobVY2ZnhLVU8xbGZrdkFqMUp3aWZ1M2ZZSzlxbEt1Nnd6MkIwOVdtcHV2ci9rQ2pMd1lsNUU1THNIZjdoVGZHTERTMjBuV1kiLCJtYWMiOiI5NzJlOWI4ZDI4YTIxZDM0MDllYzE0NjBlZjAyM2I2NjIyM2M4YTZmNGI1NTNhMTI0YjQyMTdiMzc2NzNhY2ZlIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109246441\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2025307413 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2025307413\", {\"maxDepth\":0})</script>\n"}}