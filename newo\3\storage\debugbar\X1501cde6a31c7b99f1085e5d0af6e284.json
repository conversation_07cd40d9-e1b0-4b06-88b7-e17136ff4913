{"__meta": {"id": "X1501cde6a31c7b99f1085e5d0af6e284", "datetime": "2025-06-13 08:51:10", "utime": **********.408791, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804669.22676, "end": **********.40883, "duration": 1.182070016860962, "duration_str": "1.18s", "measures": [{"label": "Booting", "start": 1749804669.22676, "relative_start": 0, "end": **********.190403, "relative_end": **********.190403, "duration": 0.9636430740356445, "duration_str": "964ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.190428, "relative_start": 0.9636681079864502, "end": **********.408834, "relative_end": 4.0531158447265625e-06, "duration": 0.21840596199035645, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44271184, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02697, "accumulated_duration_str": "26.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255011, "duration": 0.01856, "duration_str": "18.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.817}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.301505, "duration": 0.00488, "duration_str": "4.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.817, "width_percent": 18.094}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3697062, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 86.911, "width_percent": 7.564}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3886738, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.475, "width_percent": 5.525}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2022426553 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2022426553\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1286797462 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1286797462\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-913202122 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913202122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-966041 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804631255%7C2%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJTWkNMRCtubVdEaU9JVmJUY3pNZXc9PSIsInZhbHVlIjoidk5TUWtRaDZ6ZjRETFdsVlAzNVAzVnFQSC9kVGtORElqYWEzUlVrSlJhai85dllXK0Mxb292ODZjVS94ZHVrRUhqL0dELy9ldWlmTng3ZGUwN0pOMWFVY1ViV3llemhIbjJSa2cyN09xdHVrTElHeVE5dms1ZTIwblcxMjNDcUdFcS8rWCtDQWFab1MyNWRiNTFrUWRRNVpkSi95MGVvR3hPb3dYblFDUG1hVTlScmhCc0YzZlNyZ3YybXVPSG1OTDNXM1ZlczJoSC9hdnZmeDc1SVd2QXFPNVliOWJyKzBoZnRhc3QrMVlkK2NqVkQ5d214L1Nqa1Uvc00yeVR1Nit5a1h3NW5VMFNCSmxHRWdaUnZadFJlVFA2dTlUbW50WTZUcVpaZlYwYjhRSkp1b3daN0JCQXFUN1BRbnBMdUJYNkNLK3p0YnhKWk1najVVMFd5dmhZT05qRWpsbGc5Z25JUDB2UXl4eGxaOS9Ndis4TlQzTWl0T3o4R09xaTVtOUJQTmxla1NZanlGMk5TUGxjUHJsM1c5TW5WWGQ1UXFrd0Q3L2NqK1NSRHFEc0pYQjZHcE42QkVpSTB6eW1qVlB6d3BuWUlQaW1HWkx1NjB2aEVlS1J2WEdETVV5bVUyV0VTek1xV01XcGttcXBhdm40TEN6SjB6UWM3cEtIdFgiLCJtYWMiOiI5YTk5ODBiMTgxMGVkY2RiMGU0ZTM0YmUwOTcxMzc0MzNkMTAxNGVlYTlmMTYzNTZlYjFhYzMyNDQ0MGI1ZGE2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZkMnNEOHVkL3VsSHNScnB6Z1lLYUE9PSIsInZhbHVlIjoiWEJiamxlMGNmY1RXWTM1a3ZVRmlqSWhwYTUwWkRyRTdXcnZ1ZkRTWVhZcjBOMzhsdi9QRnRKSG5ZRWFhd3piK3F2dTJKL3dpV3RXOE1XeVRxZjVwbGgrZm51dkF6UkhNZU9YZFdFSG5nQlExY0ZRNG04YzdxOG1VTUtYbDFJQXNtQTdLVzRCd2JQcFpKRnZwQWpTNmsyZUhxWWVNaXFYVnBpSjU5YlREVnlYR2xMeGltRTJGc0NiZVNZUnAyOXFnQ1NDdmJ6WVZSd0xOK0tIOVl0VEFWNkt1cGg3VEREcndtZnBRYWk3aWhFZDVRZS84U1JqZEsxMzNXemlHZzRhVGRHNmZST0RVNGpSc1FNaDE1ak8wSlMzcG16QmdLRDNNd1RibzRRTHFrY2piaHk3ZG1wMVRRbEZxL0orOTF2N1N2NWs3RTMyWHpYclRvQmxvN2FMSVFPQ3RuMUZVc0hyUU9JWkl0VnRVc1JGM2wrN1JmN0g1YUxrVmwrUmVTanV5VFdFVXVnV2lFa2ExaG85OFdpK0lwSldnS3JwSVdCTWRJdEFUVlNEYmVwa0R4d2FNUXJFTXlyRG5LdmlsU3hneDZmblhIbkkxK1hSV3FQZHhmRVl2eXc2WE9hVzFXanYreFBlMnY5VnVWc1FiYWNaV2R6T2NOV2hybXM4K1ZpczUiLCJtYWMiOiIyYWEzY2E1YjY5NjY2ZWI0NDAzMzA4M2MxYzNhODY0MzZkZjc4MGJlMzRkNzUzZmEyY2NiMzhlODY4OGFmNjE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966041\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1860504035 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860504035\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1470838560 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:51:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxOYWVGeGhrWVE2RXM1Q1ZXZTJPdWc9PSIsInZhbHVlIjoiN3BPeTRxYzlHOVNVS2IyLzlrTndwSnVRdWhFclF1Y3hKbDBwZlVwSG1XWVMvdXFyY290ak54M2ZUOU03eEdHS3Y4NmF4NkFBamFwNVFOTitzS1JGWWV3VFF0TnNFc1A3a2lUaHdDQTlNSkxhbFV3UFFmR2d1SXRKNnZyeGVSbGQ5QnBUQU9WTlZrSFMvSjhicU9BdmNXYzVmazliYzcrSEc1dEo5SGVkb1YvUytzSE1ZSnFnMm02MWRWelRWSzBndnpQZVZSWDQ1QVN1QzJOUWNNb0E0c1ZtMTFLVnBkRFpxbWpRUHdpaUdPeHJLdWpsQnVJeUVCMUdocGI0OUd5c294c1VwRmVFNFNreVhLZlVYeVN5T29wWENUdnliTVd0WDNsUHp4b3V0T1d6WmNKTEV4NWV0VDQ0UHdUVDdyaUpIM0dET2NQRzZCY2dEckpDVWErV0w3eGt3R0dOVWxOOGYvV3JBTGVQSHZzM09oNlZoZnVndTdPeGtUWWtFSVJuYkFxMWdFNDdFSzYycUVjZWZla1NmckVZNmpBcXRmaW5qRG5KcFJuM3FLcGNJVWxTTkgwNFBiSmVKVVhsc2RZeURuSFRIbkM1WW9iMy9ZVHY2T0NjWmZyelpWRDVRcE03MytWTFpoL0c3ZlgrQ3U0dTh6LzUyZjcrYkc2eDFVMTAiLCJtYWMiOiJjZGE1YTM1YjAwNGFhYmViZTA0NTdhNTNjZmM3NDcxMzc5OTk0NTA1YTNhNDA0NmZhNjMxODM0OTljNWNhYWMyIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjFkaEFtQTJlSStFV2JsVlR1eFhGZ2c9PSIsInZhbHVlIjoiSmdCS0hmeTBwMGJqOEs3NS9zd2JWNlM4ejc5ZmQ3aUMzemMzN3hCb1dUY2F2T0FqRnFySGc3QzBsL3JRa0puVzVkUSsySTMxUUpyQkQzS3J5YW9vd1lPOWpyQmllbHJrSjNPeUZ2MmlzVENMQWZLaEswUWVHZTVxSVl6RnJYOU9ucmkxa05NaEU1dmpveUR4RHJsUytWTjZFQ05scmk5aTZqc2d1a25pWkU1T3FKZW1iVUhBbTBQeEthV0J6UytyWm9hMGF5N0Q2WjFNMWhBZE9wNVB4TTkxUW1xUXVoY1k1azFyclNGc3c0UlU3UTNQd2VlRTZQT3pnVVVXRlRXQXJiMG4wR1YzLzBpa0RNcHRlellJWUp2MXk4M1hINUY4ejVBd0l2K0J6VHlveGZHdFRQKy95dFlHa1VxVmtGWlFrbXV2VUlYb3JNVWJqUmpicFBpa3dFekVLV1VIcTdkelFVSXkrZzRkeTVTWEZnRExzM3VjQVpsYlpvN0dJVkNrSDJCRXF5Sm84TGw2bkhDTnlySEJTZm5GUi9HRVU5S3huWStoOHpqWUdMQlBHR0F6ZTMvOXcrOFJkOS9JKy9IRVN6SWVCUUgyVDhpUTlQVVZBUkRBblNDWTBoM0drVFJYUW5SdjE2TWZKY2pid1pUT25Jcmo1OEVFSDNqdlJhM3kiLCJtYWMiOiJlODE2M2U4YWI4MmZjYjliNWY5ODg5YTkxMGQ3MzE1MjM0YTY1MTRlNGRiOWFkOTU3NDA3NDFmODYxNDk5OGI3IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxOYWVGeGhrWVE2RXM1Q1ZXZTJPdWc9PSIsInZhbHVlIjoiN3BPeTRxYzlHOVNVS2IyLzlrTndwSnVRdWhFclF1Y3hKbDBwZlVwSG1XWVMvdXFyY290ak54M2ZUOU03eEdHS3Y4NmF4NkFBamFwNVFOTitzS1JGWWV3VFF0TnNFc1A3a2lUaHdDQTlNSkxhbFV3UFFmR2d1SXRKNnZyeGVSbGQ5QnBUQU9WTlZrSFMvSjhicU9BdmNXYzVmazliYzcrSEc1dEo5SGVkb1YvUytzSE1ZSnFnMm02MWRWelRWSzBndnpQZVZSWDQ1QVN1QzJOUWNNb0E0c1ZtMTFLVnBkRFpxbWpRUHdpaUdPeHJLdWpsQnVJeUVCMUdocGI0OUd5c294c1VwRmVFNFNreVhLZlVYeVN5T29wWENUdnliTVd0WDNsUHp4b3V0T1d6WmNKTEV4NWV0VDQ0UHdUVDdyaUpIM0dET2NQRzZCY2dEckpDVWErV0w3eGt3R0dOVWxOOGYvV3JBTGVQSHZzM09oNlZoZnVndTdPeGtUWWtFSVJuYkFxMWdFNDdFSzYycUVjZWZla1NmckVZNmpBcXRmaW5qRG5KcFJuM3FLcGNJVWxTTkgwNFBiSmVKVVhsc2RZeURuSFRIbkM1WW9iMy9ZVHY2T0NjWmZyelpWRDVRcE03MytWTFpoL0c3ZlgrQ3U0dTh6LzUyZjcrYkc2eDFVMTAiLCJtYWMiOiJjZGE1YTM1YjAwNGFhYmViZTA0NTdhNTNjZmM3NDcxMzc5OTk0NTA1YTNhNDA0NmZhNjMxODM0OTljNWNhYWMyIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjFkaEFtQTJlSStFV2JsVlR1eFhGZ2c9PSIsInZhbHVlIjoiSmdCS0hmeTBwMGJqOEs3NS9zd2JWNlM4ejc5ZmQ3aUMzemMzN3hCb1dUY2F2T0FqRnFySGc3QzBsL3JRa0puVzVkUSsySTMxUUpyQkQzS3J5YW9vd1lPOWpyQmllbHJrSjNPeUZ2MmlzVENMQWZLaEswUWVHZTVxSVl6RnJYOU9ucmkxa05NaEU1dmpveUR4RHJsUytWTjZFQ05scmk5aTZqc2d1a25pWkU1T3FKZW1iVUhBbTBQeEthV0J6UytyWm9hMGF5N0Q2WjFNMWhBZE9wNVB4TTkxUW1xUXVoY1k1azFyclNGc3c0UlU3UTNQd2VlRTZQT3pnVVVXRlRXQXJiMG4wR1YzLzBpa0RNcHRlellJWUp2MXk4M1hINUY4ejVBd0l2K0J6VHlveGZHdFRQKy95dFlHa1VxVmtGWlFrbXV2VUlYb3JNVWJqUmpicFBpa3dFekVLV1VIcTdkelFVSXkrZzRkeTVTWEZnRExzM3VjQVpsYlpvN0dJVkNrSDJCRXF5Sm84TGw2bkhDTnlySEJTZm5GUi9HRVU5S3huWStoOHpqWUdMQlBHR0F6ZTMvOXcrOFJkOS9JKy9IRVN6SWVCUUgyVDhpUTlQVVZBUkRBblNDWTBoM0drVFJYUW5SdjE2TWZKY2pid1pUT25Jcmo1OEVFSDNqdlJhM3kiLCJtYWMiOiJlODE2M2U4YWI4MmZjYjliNWY5ODg5YTkxMGQ3MzE1MjM0YTY1MTRlNGRiOWFkOTU3NDA3NDFmODYxNDk5OGI3IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470838560\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1966228078 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966228078\", {\"maxDepth\":0})</script>\n"}}