{"__meta": {"id": "X34b43731774bcea8650d46e3eee0e571", "datetime": "2025-06-13 08:59:20", "utime": 1749805160.313685, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749805156.784384, "end": 1749805160.313713, "duration": 3.5293290615081787, "duration_str": "3.53s", "measures": [{"label": "Booting", "start": 1749805156.784384, "relative_start": 0, "end": **********.387835, "relative_end": **********.387835, "duration": 2.6034510135650635, "duration_str": "2.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.387866, "relative_start": 2.6034820079803467, "end": 1749805160.313716, "relative_end": 2.86102294921875e-06, "duration": 0.9258499145507812, "duration_str": "926ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50760256, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00834, "accumulated_duration_str": "8.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 70}], "start": **********.589944, "duration": 0.006860000000000001, "duration_str": "6.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 82.254}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6148849, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 82.254, "width_percent": 17.746}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Kc6Qi1Vl7K82y3V94YtoWSezXHBI7Fyj8MVsWxAR", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-436882062 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-436882062\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1467958289 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467958289\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-641820536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-641820536\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1452471806 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=p1vqtu%7C2%7Cfwq%7C0%7C1990; _clsk=1j3nvos%7C1749805121148%7C1%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZMTVpYZ3lqSWEraVZLMzVydFJBdVE9PSIsInZhbHVlIjoiSXNYUGJLL0dRMXUrVG1kOVJnWDRnMHp3TzRSK1pwZXUwVlJ1TEZMUUk5T1VRclcxcHY4RUh6cnZaMVNBMjYzbE9tQ0psZXNsOXdUd0V5RkJtVEVuL3Q0Qm9xejNEM24vK1ZXTWdmNTFtRGdiR3FKdUoyM0x2WUljWmllZTVpYUNUU21sdkJMMTdmRXI1MWJBMGVsQnBiU3lWYXhQV3RTUUl5ZS9LKzZPOGJ6QjJSejM2QVNidXNlQlZSZnB4emlQcGJ4Zlp6NnFiYk1rVEYzUWNmb0txOFR6NFMrdld6OFJ6QU12bEtNVjBwVTgxY2ZmMjBVT21TOFowK0VqOVR4N001eE1JVUhBZlNqMHBJYkd4MVZzOTROcDZoOFR3UXVrSGlkcGd0NDNjMDI5Z0E5cTZXY1JYd3hhUnJqbFpNMWFqUmRTMGM5S00xOVBvM05FNklCMnBzaFo1d2FVdzc0aXhpUzI4ZGFTSmNwc3BZeldqdzluRVF0bEN5ZngwWjFEUDVuSng3M29WZ1dGMzRmVEc0WXR4WWxxQ0s4UVB2YU9DZmx2dXFWcGtXRUFYelpDdVF0SktPUTlIakJIbCt0TlZXaC9qVnNkamxHeHRwN1NtKzgxdmVWZm1pNXdYUEZEbFdwdGY4N0hwVTFKMWNYdnRTcUo4ZlpRZ3A4dHhZRGciLCJtYWMiOiI5MWJjY2U3NjBiZWQ4ZWQzNjU1MTlhNjUxMjFiYWZlMDRiMDhiMzg4MmY3MTMxMTM0MWE1NzEwZmIwZjY4N2E5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IndudDNFNVRrdnJBbC85YjBmUExod1E9PSIsInZhbHVlIjoiZzFmUGhSZU0wVU5Kdm8wd3BSaWpqbEdZeVFIMDNVY0RRKy9tb1hzdHgzRkpmMm11UnZGR3dybmp3RngrNXBCVVovQTBtV3RmdTRCTDRaZ0F3cGF3bnJWam5nR210N0Eyd3RDRy9kUkVhUXg3QXVlbEVQSUhaSTl2NjR3UU1tNHZFMjZ4a3E0MVRROGI2WWxQRys0R2lvSlNQQXRlUUMrSzZiaDU0QXVXVDFvUEl3U01KRzR1dUdrbE1iNlRSSFlRcmZxVDVrekp4WFN1VVRpZU5qcTVBSE1zYWpGTW9nTWdwWXJRckdXMnZOaWpHOXlHZVp2OUdFM0txQ0xXaHlxVXhBeXNGOEZ3Y2pNaHNiam50c0hiOVZJRXUyUnJpUVYwOStGajhObW4wUlZOay9KSWdQWERDejJWaldBcTlQZU81VVUzeU5GdjhuRThpU1Fwa1F3Ui9YWjlMcTgwdytDOVdWbE1rWnlocEcvaGZXYlV4L1JHRTIwc2phaFRsUjRTYklaSW13cHpvbnNIbDlna2g2SE9oM3BidlMzUEVEdVlTT2NwRStPNHpWUHUvZ3h4TDVMQWVJMXJYTW1DQlV1V2dtZFdyekhEd1ZZaTZKN3ZrTEVucXZ6RUdpRUZIS3UwcFRmRXVnVEc5QVU4WTBoR0luSUs2V1h3SkxEc0sxYlYiLCJtYWMiOiJjZjI0YzIzMmE1OTE0ZDAxNzllMDUxMzlmNzVhYTAxMjQ1YjgyNjIwNjZlMTRjYTZmYjMzOWExNTIyY2ViZjdhIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452471806\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2008688797 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kc6Qi1Vl7K82y3V94YtoWSezXHBI7Fyj8MVsWxAR</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">EULTRGsO3OKjCOOciIIO2V9k5c3RmUfV6ReutoNu</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008688797\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1140761840 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:59:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhkZ1loOEZrSy9hTDUzWTFsakc5Z2c9PSIsInZhbHVlIjoiVjFaY1VjYVBUQWNRV01uYmJrWnU3YlFJK3BKK0N4a0hxQU5qcWp0eDVkMWFXWk5laE8vRTRNSDhmclJqaTI0SjlOY1JsWkY4dVpUN3JDSEVaZmhtTGQ0NW93K21rVHVIa3FGazB3VlNIRDl1L3p5WjNMQ0FkMkM4RWJhZUpEN29udzIvaCtmcCtjUHpjeDhyVTJ1SE03TmthWVlJaEh1cmJlQmpVY01EbE80TUh5anFLblc1YkJjcERwMDdiWThlQWswTWpibVZYSXdiNlJKREFvMkl1L0JZdmdxelNiS2lxeGpqWmVjbzZ3VkhKNEljbHJVNWc3TzRLNm5rc0tUM2JTdHZNd0lvZDlyVjc3ZVc4N0ZDdFZOV0t5ZzlOVG9ha2tDQmw1NFJSVE9ra3BBdE4vMDlhbzNNVEVXUHNXUzZ2cGRQUHVIdU16VVFvRnh6TVR2K0xJYUJ0d3VQaWtKSXhzYW9iZHQrMGpJaHl2cmRvK09oQW56dzZBUVVpOWJpa0hTWGpnNk1WZlJsbTVXZEE2elVUVGJsQldaRllJRGJSSng4VCsrb0ZpY3VBWnpXN25tSmFrbEFmQlJ2eWZKUVBqelluQkx0bFpZUDMxb2c4eC9KbHRPYU8wK1ZXWkRXNk1EMktHczVsM0Y5Mm5QdmVCbzhicFRJWUNsYUVWbnYiLCJtYWMiOiI1YmI4NDNjNmE0N2Q0Y2NhMTRlZTViZTc2MTNhMTJhYzZjNjdjNzRiMjI2MTMzY2FmODcyZmJhN2QxYzFlMWIwIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:59:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNuNnJyYUN5SFU0Zitsdk5URko4VUE9PSIsInZhbHVlIjoiYklITDBnaDJrYkhPQ2VjdXdSRzBmZ2NZcDJOWHVTekFPcGd5QUcvZHFNRFVnUm5QSCtXN2FvVXJiRG0rcjBwdzF4QWlnaUNwWWQrWHBnOW1tcURhdVlteVNmcFdyU2oxWEVPbmFQT3lSUkt0MmlnYTlUcE8wejcwQ1o5dlAvTG52WUxPNFlvazIzeFlLRFZHazduNFFrWG5RMUpEZmcrVjZPbGEzb1J3QnRaQm9MUThUUWQ0QldudlRRVC9hZmJMWnNRdVAzd1NuMmJrdWpBQlRPYXFYRjdLcmh4aWNJMURVMXE2d1JCTEsxL25CUVlsejJUL2FGVncxOTdwR1Uwek1MSnphaTQzSjk1a0tFWG4vNVZtaytQNkloWTErdDBzWDRJVUk3SmhYUjVzOTkvaitaa3hOeUVqaHMxRm8wVGRhZEhSSDJPTUVraXNsVktQZE42RG9wWXR2UzE2aXRRQjg5eE45aDh2S2xvRjZTdElNWTZoL2w3K2c0eGhqLzBCeUp2YTNzQTFaek14a2xOc2ZQcFpxWk90VGswNzF0Z0Z6blhwN2VLK1VrTGh3TGZENkdvbEJ0dGhiQXU1L2MzNTg1eXV6bjhJa252STEwUmh6bGVMWlhnb3A2ay9GU0trQVovTU1wRkVqYXBuOTBKQjhha2lZUHlqQytCamgvTVkiLCJtYWMiOiI0NWU1ZjE0YmZkNzdhNTQwMjQzNGJmZDM3ZmE2NWE4ZGM1NTNlZTYzMDAyYzhiNTk1NjJjNmUyYmQ2N2U5ZDczIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:59:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhkZ1loOEZrSy9hTDUzWTFsakc5Z2c9PSIsInZhbHVlIjoiVjFaY1VjYVBUQWNRV01uYmJrWnU3YlFJK3BKK0N4a0hxQU5qcWp0eDVkMWFXWk5laE8vRTRNSDhmclJqaTI0SjlOY1JsWkY4dVpUN3JDSEVaZmhtTGQ0NW93K21rVHVIa3FGazB3VlNIRDl1L3p5WjNMQ0FkMkM4RWJhZUpEN29udzIvaCtmcCtjUHpjeDhyVTJ1SE03TmthWVlJaEh1cmJlQmpVY01EbE80TUh5anFLblc1YkJjcERwMDdiWThlQWswTWpibVZYSXdiNlJKREFvMkl1L0JZdmdxelNiS2lxeGpqWmVjbzZ3VkhKNEljbHJVNWc3TzRLNm5rc0tUM2JTdHZNd0lvZDlyVjc3ZVc4N0ZDdFZOV0t5ZzlOVG9ha2tDQmw1NFJSVE9ra3BBdE4vMDlhbzNNVEVXUHNXUzZ2cGRQUHVIdU16VVFvRnh6TVR2K0xJYUJ0d3VQaWtKSXhzYW9iZHQrMGpJaHl2cmRvK09oQW56dzZBUVVpOWJpa0hTWGpnNk1WZlJsbTVXZEE2elVUVGJsQldaRllJRGJSSng4VCsrb0ZpY3VBWnpXN25tSmFrbEFmQlJ2eWZKUVBqelluQkx0bFpZUDMxb2c4eC9KbHRPYU8wK1ZXWkRXNk1EMktHczVsM0Y5Mm5QdmVCbzhicFRJWUNsYUVWbnYiLCJtYWMiOiI1YmI4NDNjNmE0N2Q0Y2NhMTRlZTViZTc2MTNhMTJhYzZjNjdjNzRiMjI2MTMzY2FmODcyZmJhN2QxYzFlMWIwIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:59:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNuNnJyYUN5SFU0Zitsdk5URko4VUE9PSIsInZhbHVlIjoiYklITDBnaDJrYkhPQ2VjdXdSRzBmZ2NZcDJOWHVTekFPcGd5QUcvZHFNRFVnUm5QSCtXN2FvVXJiRG0rcjBwdzF4QWlnaUNwWWQrWHBnOW1tcURhdVlteVNmcFdyU2oxWEVPbmFQT3lSUkt0MmlnYTlUcE8wejcwQ1o5dlAvTG52WUxPNFlvazIzeFlLRFZHazduNFFrWG5RMUpEZmcrVjZPbGEzb1J3QnRaQm9MUThUUWQ0QldudlRRVC9hZmJMWnNRdVAzd1NuMmJrdWpBQlRPYXFYRjdLcmh4aWNJMURVMXE2d1JCTEsxL25CUVlsejJUL2FGVncxOTdwR1Uwek1MSnphaTQzSjk1a0tFWG4vNVZtaytQNkloWTErdDBzWDRJVUk3SmhYUjVzOTkvaitaa3hOeUVqaHMxRm8wVGRhZEhSSDJPTUVraXNsVktQZE42RG9wWXR2UzE2aXRRQjg5eE45aDh2S2xvRjZTdElNWTZoL2w3K2c0eGhqLzBCeUp2YTNzQTFaek14a2xOc2ZQcFpxWk90VGswNzF0Z0Z6blhwN2VLK1VrTGh3TGZENkdvbEJ0dGhiQXU1L2MzNTg1eXV6bjhJa252STEwUmh6bGVMWlhnb3A2ay9GU0trQVovTU1wRkVqYXBuOTBKQjhha2lZUHlqQytCamgvTVkiLCJtYWMiOiI0NWU1ZjE0YmZkNzdhNTQwMjQzNGJmZDM3ZmE2NWE4ZGM1NTNlZTYzMDAyYzhiNTk1NjJjNmUyYmQ2N2U5ZDczIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:59:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140761840\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-643155480 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kc6Qi1Vl7K82y3V94YtoWSezXHBI7Fyj8MVsWxAR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643155480\", {\"maxDepth\":0})</script>\n"}}