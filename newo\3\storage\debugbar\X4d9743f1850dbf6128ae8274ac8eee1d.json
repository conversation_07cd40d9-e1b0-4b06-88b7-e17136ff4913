{"__meta": {"id": "X4d9743f1850dbf6128ae8274ac8eee1d", "datetime": "2025-06-13 08:58:36", "utime": **********.937737, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749805115.115504, "end": **********.937773, "duration": 1.8222689628601074, "duration_str": "1.82s", "measures": [{"label": "Booting", "start": 1749805115.115504, "relative_start": 0, "end": **********.545917, "relative_end": **********.545917, "duration": 1.430413007736206, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.545946, "relative_start": 1.4304418563842773, "end": **********.937776, "relative_end": 3.0994415283203125e-06, "duration": 0.3918302059173584, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45157728, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.770636, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.801954, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.897552, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.913545, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03233, "accumulated_duration_str": "32.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6696389, "duration": 0.00738, "duration_str": "7.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 22.827}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6937149, "duration": 0.01512, "duration_str": "15.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 22.827, "width_percent": 46.768}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.722559, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 69.595, "width_percent": 4.33}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.774126, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 73.925, "width_percent": 4.238}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8065538, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 78.163, "width_percent": 3.928}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.849185, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 82.091, "width_percent": 4.114}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.863319, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 86.205, "width_percent": 4.516}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8751569, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 90.721, "width_percent": 4.423}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.903031, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 95.144, "width_percent": 4.856}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbtMkng0tvxhKHxUEliFrsxOT4cipjUmlsYT4kSB", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1835820323 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1835820323\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1686573837 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1686573837\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1591805553 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1591805553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1154620839 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1734 characters\">XSRF-TOKEN=eyJpdiI6ImRxVDFOQnVtMml5bno1eERhbDByNVE9PSIsInZhbHVlIjoiMWZ6WVkzcXlUQXVCQTlMQ21jZDVEbDRKM3prVERJM2E0V1NwNmxFVXlLUHJyMHhPY0VDSWx6YmQ3eUxSMG5ybmxENEVyVUJ0NEJaazFLWEYvMGdyRFc2RmUzWFZQbUJkV1hQai9VOVBGQ2Vtelg3M3pCeUZma2ljVU9RSVpSR0lQSHRFQUMzR3B6Q3Q5bkhYbGZnanN6cFRuZ3E5L3c3VmQzREZaK3pJajdsWFRJLzBHV0VVSk0rNVRTRjBNbU1KOVdpTTVwS2dWNDB6ZVgvRzlFMTk4eVc3ZVpDWEtwTHNlOG1BRFlxZGUvMWFEdE55cU05Qjh5aEdUUlEzUkhTL2h2N0xLTFN2dkpWNjNNd3FrQjZ3N3dlT1JIdFRRYUdKeG5VOWRUQjl2WDZ2cUgrWE1qYm1za00zUHc0Y2FKVzN2RCtkZkFxKzBtZUp1aE1obGlJMEloNU5OZzc4cWVvVlpCVzM1MHlVTWljSnArUkZFZlIzWU9GRG5tcUVSQm5xUklvR0VmMXRhSXNDSlhrY1B4VllVYWJiaGduZEdwL2dpT25kaVpiaGNTdDRtR2xzREk0bXNNd09nQ2VOWktyMmo5aDdkUGloL3Z5N2xRQzJQVFp4V1Rrb24wUEE2ME43ZHFiTGlWOG4rdXRTeXBzNGI4Ny9XWVhCZWFYeExQNHUiLCJtYWMiOiIzODljNDcxYjVjZTNlNzI5MzI0Njk4YWRjNDA4MmE4YmZmOGJiOGEzZGI4Nzc2N2ViODQzY2EyYWE4MGIzYTRhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFWMnJOTlc3WlY1dEsyczVjMUdWMWc9PSIsInZhbHVlIjoiREJJVTYvTGtWVzBPcHFlRnoyQXVmd2Y0MDNxOFlhNXRLaHMvU0VNa2IyNFlncExGMWl1NG45ODd4MVpaNmdscE5ZL2FhL1NEcWZCMVBac3JBMEZITDJwRW9XSGZYQkNCOTdiTFlSKzdvcUR3N05Lc3Z2dFVUMU5kTUtvOXNNRERNTTNWRDY2cnJWVnhEVEdWSjhrSUkyVUhWV0xQMStyVStNNmhlZGJITlA1M1ZQVjdpd2UwK0xUTk91d3VTUjhwTXZwVmNuRmsvdnNwNnlmYzFUb1ZMV0U4SUJnNlhqVGZNSHUzbHZNNnZQS2dUalFvTHNNbGFQd3BtdDdhalduUUlXZVdUYXlBTForNFBHcTJoUm1iQkZ3T1BDK3ZNcnBaMFlLakFXSUtnYW5JT1dmckUwYmJFK3BhUk1oclEzcWxaaUJyTmIxMmpTT2hLVCt2bjJzdmpkbTl5TEJUWEM0Rk9rbjJxRmt1OHIwcThGL25DYXF5bzdYek1xTnkrMkxSRE5mY2RqMmxUN0J3K2NWSzkvbzRXejdlV2NXMGxSOUF0aDYvN3ArR3VWaU11V2c3L0xTMkRoRG4zdVZQVDNYRWlEaWFGOXdYUldFc01Pb2d4ajJiNVZaKzNmWWI5OCtaNTREV2s4Wi9hQ2xMdklwQnFTdmpkcVRFWXhoeXp4QjAiLCJtYWMiOiI1ODBhMDBmYTc2YjNmMDk0ODU2MDA5M2ZkNGQxZDQyODliMmU1OGMxZWE5MjRiMTQ4YjA5NTMyNDA3YmE4YmRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154620839\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1227899058 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbtMkng0tvxhKHxUEliFrsxOT4cipjUmlsYT4kSB</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MTohpo65KcACBKrCraeyPND1UGHJRA27C9sEZwGZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1227899058\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1444551859 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:58:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjkrNDc4TFc1ZmtWUWJjakhmbmxYaXc9PSIsInZhbHVlIjoiNjJiTVFCdU5sM05WQlV0VGpkdnJnNi9UQWRvQlRLVVlSdmFGT2g4TXZaakxZL2ZCNURUVldocmxQcWlPNS9mWVREdXZiVDlrYnQvOUJPcUI1N1o3MUxTV3NuQ2htK1lSOTN1K0ZUV09ydXhZOXNpVGZDNE4xUGY5cTh6QmU4SGNKZ09MK09wL1R1NlhvR1FZdTcrNmZXNXJZa0RhTlBpM3ZmMUZZcEQydHhRZkVZK1ZqN1dzd1dDWHpqazlVVHZwOWlDWi9Kc2FhQVRNQm91aXU1cG4vQTBLdWJIYXJKSW5Tdnp6blVGMzdRaGRCbE5qSzlZWXpncTJ3eDVVTGM0azV2NE45bitBOW1rNVhkUmtDVDlObWp4Z3IyNW1sNkZjbnlNZjZUTGxHc2xrSXh5RjBHUURpTUFDWUZmSUR5NEVZRk1Gc3hnTDNveXA1Y1k5MkNaNUlrYWxsQmt0d1JtcWJzYnozN2w4aXN3UFl2VjZrbVhxWFhCQzVyejM5Nmdud2VQeGRUOW42WHprRnFwTkgyQ3ZVbmtjYlh6ZHFHdVo5bHkwQXdLZTRIMWtHTWFaOGdXWjUreW1nOHhzTU9MQUNsK1FYTWgydFJmdko0MUYxbzZDVUpQZXFnME01YnVuL2tjVWV1dm9DYVAxcE9iOEtVM2owYXpMeWhYZ1hwaDgiLCJtYWMiOiJlNmIxY2M0OTgwMzg5MmM5Y2IwZTU5NzBkZDEwODBmNWE2NDRhMDVlOGQ1NzM5MTg0MzYwZDQzOTc5MTE1YWUyIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhTWTRYaEpCY1F3VEtOaW96S0FRNUE9PSIsInZhbHVlIjoiSjd2U2dWNGtLM25iakwxcGxFYVp1ajBRbWhwUzhvaHl5OVlkVHhwM2Qxelp2ZE84NklhMm1wbitIZVNDMi9hTlRIckVtV0ovaFNvS0x5eDl3MzAxSm1CVGRhek83eVpWT3JScFpteTRGYWIyOUpZSlV4alU2TzA4N3BwWCtFOTRJekhYTlJ5VngxelIxcWY5N1hvbzZCY1NZM3dZMVlpU2VNVDNmNDNpd2oralR5aXZTSk12S3JZemxKYUlSMC9JYUx0emxNU2lRTU1KelZOQ1gybXAvdlpuVCtaWXRldERWc0M5bGZmdTJPZ3dRZ0x2dzN4MmZFM0xJUm1Bc2gybGd0cHEzOHVtb2ZoYWxVK1JrZnY5Q0lkbWQ5MFUrSVFhbGNVbmM0VDN4MnUvRXFyUCtiQUYzVXBRQjB0dk1ubzJ3RitzVFVsd3V4L000cGpZQWJud2dZS1dEMzVOSTI5Q1JiM0ZvSGpubGVMMUtZQU9SRFZMU1psbW01V1QzTHpBN3pEaVVJL3c1aFNxdlRHTFFUVVVwYThpY3FCZzgzbWlOQ2ZGdm1ZQ3VqRXhMNXE2aHh3Q3Q4UmZNOENPVm5makw3U2Q0R2R1aTRHa0I2cEIwckxTOXR2K3FQSzZOVlJZcTRZUHpOZTJiZGFUNXdneU9aUktMYjNpRktPcklvODkiLCJtYWMiOiJmMzg0ODI3Y2JmNmU2Y2NhZDkzZTQ4MWI1MDg2NmFkMDQ5M2U4Mjg1NmFmZjc4Yjk3MGNiYTJiNjAwYWM2OTkxIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjkrNDc4TFc1ZmtWUWJjakhmbmxYaXc9PSIsInZhbHVlIjoiNjJiTVFCdU5sM05WQlV0VGpkdnJnNi9UQWRvQlRLVVlSdmFGT2g4TXZaakxZL2ZCNURUVldocmxQcWlPNS9mWVREdXZiVDlrYnQvOUJPcUI1N1o3MUxTV3NuQ2htK1lSOTN1K0ZUV09ydXhZOXNpVGZDNE4xUGY5cTh6QmU4SGNKZ09MK09wL1R1NlhvR1FZdTcrNmZXNXJZa0RhTlBpM3ZmMUZZcEQydHhRZkVZK1ZqN1dzd1dDWHpqazlVVHZwOWlDWi9Kc2FhQVRNQm91aXU1cG4vQTBLdWJIYXJKSW5Tdnp6blVGMzdRaGRCbE5qSzlZWXpncTJ3eDVVTGM0azV2NE45bitBOW1rNVhkUmtDVDlObWp4Z3IyNW1sNkZjbnlNZjZUTGxHc2xrSXh5RjBHUURpTUFDWUZmSUR5NEVZRk1Gc3hnTDNveXA1Y1k5MkNaNUlrYWxsQmt0d1JtcWJzYnozN2w4aXN3UFl2VjZrbVhxWFhCQzVyejM5Nmdud2VQeGRUOW42WHprRnFwTkgyQ3ZVbmtjYlh6ZHFHdVo5bHkwQXdLZTRIMWtHTWFaOGdXWjUreW1nOHhzTU9MQUNsK1FYTWgydFJmdko0MUYxbzZDVUpQZXFnME01YnVuL2tjVWV1dm9DYVAxcE9iOEtVM2owYXpMeWhYZ1hwaDgiLCJtYWMiOiJlNmIxY2M0OTgwMzg5MmM5Y2IwZTU5NzBkZDEwODBmNWE2NDRhMDVlOGQ1NzM5MTg0MzYwZDQzOTc5MTE1YWUyIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhTWTRYaEpCY1F3VEtOaW96S0FRNUE9PSIsInZhbHVlIjoiSjd2U2dWNGtLM25iakwxcGxFYVp1ajBRbWhwUzhvaHl5OVlkVHhwM2Qxelp2ZE84NklhMm1wbitIZVNDMi9hTlRIckVtV0ovaFNvS0x5eDl3MzAxSm1CVGRhek83eVpWT3JScFpteTRGYWIyOUpZSlV4alU2TzA4N3BwWCtFOTRJekhYTlJ5VngxelIxcWY5N1hvbzZCY1NZM3dZMVlpU2VNVDNmNDNpd2oralR5aXZTSk12S3JZemxKYUlSMC9JYUx0emxNU2lRTU1KelZOQ1gybXAvdlpuVCtaWXRldERWc0M5bGZmdTJPZ3dRZ0x2dzN4MmZFM0xJUm1Bc2gybGd0cHEzOHVtb2ZoYWxVK1JrZnY5Q0lkbWQ5MFUrSVFhbGNVbmM0VDN4MnUvRXFyUCtiQUYzVXBRQjB0dk1ubzJ3RitzVFVsd3V4L000cGpZQWJud2dZS1dEMzVOSTI5Q1JiM0ZvSGpubGVMMUtZQU9SRFZMU1psbW01V1QzTHpBN3pEaVVJL3c1aFNxdlRHTFFUVVVwYThpY3FCZzgzbWlOQ2ZGdm1ZQ3VqRXhMNXE2aHh3Q3Q4UmZNOENPVm5makw3U2Q0R2R1aTRHa0I2cEIwckxTOXR2K3FQSzZOVlJZcTRZUHpOZTJiZGFUNXdneU9aUktMYjNpRktPcklvODkiLCJtYWMiOiJmMzg0ODI3Y2JmNmU2Y2NhZDkzZTQ4MWI1MDg2NmFkMDQ5M2U4Mjg1NmFmZjc4Yjk3MGNiYTJiNjAwYWM2OTkxIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444551859\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1538167553 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbtMkng0tvxhKHxUEliFrsxOT4cipjUmlsYT4kSB</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538167553\", {\"maxDepth\":0})</script>\n"}}