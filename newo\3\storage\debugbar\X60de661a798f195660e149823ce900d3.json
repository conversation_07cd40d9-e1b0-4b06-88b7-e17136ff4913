{"__meta": {"id": "X60de661a798f195660e149823ce900d3", "datetime": "2025-06-13 08:51:12", "utime": 1749804672.000897, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.04981, "end": 1749804672.000922, "duration": 0.9511120319366455, "duration_str": "951ms", "measures": [{"label": "Booting", "start": **********.04981, "relative_start": 0, "end": **********.868973, "relative_end": **********.868973, "duration": 0.8191630840301514, "duration_str": "819ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.868995, "relative_start": 0.8191850185394287, "end": 1749804672.000925, "relative_end": 3.0994415283203125e-06, "duration": 0.13193011283874512, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 42935216, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01525, "accumulated_duration_str": "15.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.945418, "duration": 0.01442, "duration_str": "14.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.557}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.96994, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 94.557, "width_percent": 5.443}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-1864660139 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1864660139\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804669558%7C3%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5sTXo5aGlSK3FWeWJvN2RScHNYdEE9PSIsInZhbHVlIjoiL2YwbStlSlQ0SWJ0Wk5haDBubGNRcFJzQTFUbzJNRE11d0ZUcjg4bE5FeFpSNDdJQnNKcFNocWZyV0ZzQVBPMmdHOFJteGNFYXRKdW1NTUZBU2tUa1V4L2FldHhBN3lUU2NiaTRaaHpIcVpoNGtPMDRFeVY5QXl5N01kQzlzUlZlYlhqMGd1OGhiaTBpQk12eFI0c2VSTVBhS0h5aWRYay8wQXNIVjVnb3puSU9DOTh4MVZKRXhzWnR2Vlp6T3VwTk5aMFpMTXdoRmNSVC92OU5HdmNzSVFweHJRTWUvVWw5Wkczb2xvNDFNZy9SUTBzMngwR0diVjJEZmV4SWx2Zld5VlFqc1ZDSUxWODF1d2ttSW9hSi9ueERHK3phaGtIWCswQzRnVlN0dXNCTStWTTk1MnhpQm5YMzAyOEsvZ1VHRDB2UXE3V2VISGJqYlY3cUJCN29DSEtId1YrWFFMc0M2ZTY2eVhTbWhQamQvVWdwK3R4VEsvSjRkeUFSTEhxTnBpT2l2TmtrVlA3RGhHQ3MwZXNLMHQ0UWpqUWNUbUx3dlV3eXVybnhKWXZxZlYxR3N6T2JzSHcrWVZHSDZjalpITURuRElONEdhMlFNWXNhbW9yTG5zSkNkWktzc2Y5MnBKN3dQYnZoSjRXcjg4akZKV1Q5a29zL0xjQjlZaGQiLCJtYWMiOiJhMjJjZTkyNWIzNzYzOWIyNDQzNjhmNzQ3ZThiYTdmOWU3YjhjMDQ5MjdhNWE4NTM2ZjkwMjRjYWExMGMzNzU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVGRGRJU1RNU3BhTHdtU0dPbUNDS2c9PSIsInZhbHVlIjoibnAyckw1QktYQ1lRL1ROTTM4WG9QVEdESGExNms0WldBSTEvbVRjeDhIQjhpeVVOalA2MzFzaHZnb1JmcC8yenhjVU1Cbml5M3VnaDNhcVN5c3J6c3lldnhzUjhiQmRIVUJTTUlsWm1tNFNaY2syQ1hYcGZpdVpwWkE3NjdQZU1mYktlclpQNDViRlNYa2R1amtUNnlodVVhMkNWTEhKYXNYMjF1Y1RxYUtIYUNZUCtuU1IzeUFKWmtuQUVvK29IbGJISjFQV2hsZWsya2UyQWdyTzloNHh1Um9QbWdEa1dCRmtCRGVmV0t6d2Y1YlRMQkxqVmpTazV5YjdQWVVWVEdlUGd4NUlhY3EycGdyMHpHazN2RVRmMFRVZ1J2bVVORk9FdGJBbnA4MGp3N2VDak51OFJXTGQ4azAxSHNNWlNmNytjTWZ4blM0UWV2ZGtURUd1SU1ydUl5L2s1TXhjODZENDZUdTV4RmlOSTAwWjd2K2xyZDlQcEQzZDl6OUtLbjdZWVpiR3BmSUlYVk5NVTI5NXJIbDAyd0tMWjFadkE1dWRzRUNFV1F1T2Z5L0RBaG04eTM3WEJIT3J0RWdpNThLNDFlNEpBc0RwZGdLY3Y4WlhSY2d6U3NmZ1hscGRCWkRtckpaNXZzN0V1aUd5QUlPSEZzMTN0eWM3cnNvYjIiLCJtYWMiOiIzZGNkMGQwZjNkZGQyOTc4NTg0MTdjOThjZGVlNGJhODBjMzc1ZGZiZjUzZTNjOTMwZGZkYjEyYTU3YjQ4NTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1880278590 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1880278590\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1570640860 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:51:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iklld2pkakdnSDUyK3o4SVdHcXJqSUE9PSIsInZhbHVlIjoiYlZ5ZWFFdlhhejRncHR6bnV5d3pxTVFqZWNmT2g0NlptbHE1U3BnU3M1azUvRmlwckNJSCtNTmpIZWIxa2xuM1hZczE1TVdpMU93ei9ZUUE0UzRqRTA5VGhSUmFZZ05DRndKTjBpRGs3RCtUWjZKdTZmMW12M0owVndaVWlON01OTjVYRmFSSVAvK2pWZlJTdlNLVEl4SWZFVVl4Sm1URjd0RzcxM0p4WDhDWmwrcjV1b29aWkFxeVhabWx3V3J4SjgzR29uUng4emhPeEgxRGp5RnEvbUxjSG9vcVZ3RVdlSjJPZXNZd1h0YzkzMFVkaURjc1R6Rko1RERUaVUwdS9IYmE2OWRCc1EvQ1FwTTdFWXhzOVdCM0tkQkRQcWJyNmVQbWhCazNPVFFJKzRUVkxZdmhYSm1VSWJJOTJ5UHRENFdvVUxVWURiUEk3eVZ6T0ZnVFNxYzhnSTJhUDZOcW5Kd0JqS3FVc253UHBNY002M1c4NUdCYmxrRXlOWEVXMWNrYUpGZUtPU2ZidU9COW4wZ24xWW9WUGorN2NpZUpQaUlTd2wvdnNHeERUT0J6bEdKdUQ4M2FmL0I4bHlwaTFJZ3BTaWw5bEExM2tVZzZYUzR1d0xRRUhFL3NLc0JhZW5JRjJWRWtUNmhoNk1JZktNMVBUb20yUWxaRnh6c0wiLCJtYWMiOiJlYjY4YzYzMTQ1ZjNhZDQwYWM5ZWNlZmM5Y2U4MjliNTRlYzMxYmY0M2Q0MmI2YzRjODViMzE2ZTVhYzQ4OWZlIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:11 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlBCWnRIMWJxeWFsTHBkQzlsK2IzK3c9PSIsInZhbHVlIjoiNFRKLzluTnVsUlpSRUFBNkpScllCV2Qyb1dKOTNES0YzZjRkdUMrTUxmRmxZRk9pdnM3TVhNOWxsdE1ja2ljS29wYUNZWXNHZDZBaXlWeUZNcHo0ZWI4NDZSM29pSUx2a3dyOFl4eFhYUkFkeUZ2RkxrMUlQQSs3czg0ZmxtdkVPWVVycEhKdjNDSTVMaitPaFN5aXlvYllpbXpIY1dVbmZMK2cwWkxlTzdPNE5lbGxtVHNRam9TMzNsTHRDVldrYWx5c2k3MUdpMkNnNnJFQldYejZJMUhob0pqUFJXckIzcjVFTUR1bm8zNXR3dG1TWGlFbk95ZlFyL0c3cEZQMkFocmN6RDNuMkQxKytVNDZuS05JWnVPcmxmWGxwamczL2FzVDZnV2ZKZlBIaWVEdTdDcVJtQmduTjJNd1Vyc2I4V3cyMUNMM2xUb2ZyZkF4L3UxZDNqcHc5ZGxjRGVyaWpTeUtPOWI2cExhK0svQTFZMG02MGF3bHhCR3owb0tzVzM1SWZIUGFla254Z0FUT0hZQldrL29oSngvSmE0RkRwY251b1VCS1dIaXF4VVQxcW5aMmRJUG5NanU1RUVSeFlrbWNSNklXTGoyRHlUM1duejNnQWlOL2JNSXFlR3B2Y25PNkNMNFhUcmpCZ3FwbUdUSnljbHhzUS8rcW5MVUEiLCJtYWMiOiI3N2RhZTZiZTFkNWIxN2M2ZTBiYThkNGQwMzAwZmMxMDJhMTEwMDVlZjk3OTUyNDlkNjg0YzRhZmZkOGFiNjA0IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:11 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iklld2pkakdnSDUyK3o4SVdHcXJqSUE9PSIsInZhbHVlIjoiYlZ5ZWFFdlhhejRncHR6bnV5d3pxTVFqZWNmT2g0NlptbHE1U3BnU3M1azUvRmlwckNJSCtNTmpIZWIxa2xuM1hZczE1TVdpMU93ei9ZUUE0UzRqRTA5VGhSUmFZZ05DRndKTjBpRGs3RCtUWjZKdTZmMW12M0owVndaVWlON01OTjVYRmFSSVAvK2pWZlJTdlNLVEl4SWZFVVl4Sm1URjd0RzcxM0p4WDhDWmwrcjV1b29aWkFxeVhabWx3V3J4SjgzR29uUng4emhPeEgxRGp5RnEvbUxjSG9vcVZ3RVdlSjJPZXNZd1h0YzkzMFVkaURjc1R6Rko1RERUaVUwdS9IYmE2OWRCc1EvQ1FwTTdFWXhzOVdCM0tkQkRQcWJyNmVQbWhCazNPVFFJKzRUVkxZdmhYSm1VSWJJOTJ5UHRENFdvVUxVWURiUEk3eVZ6T0ZnVFNxYzhnSTJhUDZOcW5Kd0JqS3FVc253UHBNY002M1c4NUdCYmxrRXlOWEVXMWNrYUpGZUtPU2ZidU9COW4wZ24xWW9WUGorN2NpZUpQaUlTd2wvdnNHeERUT0J6bEdKdUQ4M2FmL0I4bHlwaTFJZ3BTaWw5bEExM2tVZzZYUzR1d0xRRUhFL3NLc0JhZW5JRjJWRWtUNmhoNk1JZktNMVBUb20yUWxaRnh6c0wiLCJtYWMiOiJlYjY4YzYzMTQ1ZjNhZDQwYWM5ZWNlZmM5Y2U4MjliNTRlYzMxYmY0M2Q0MmI2YzRjODViMzE2ZTVhYzQ4OWZlIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlBCWnRIMWJxeWFsTHBkQzlsK2IzK3c9PSIsInZhbHVlIjoiNFRKLzluTnVsUlpSRUFBNkpScllCV2Qyb1dKOTNES0YzZjRkdUMrTUxmRmxZRk9pdnM3TVhNOWxsdE1ja2ljS29wYUNZWXNHZDZBaXlWeUZNcHo0ZWI4NDZSM29pSUx2a3dyOFl4eFhYUkFkeUZ2RkxrMUlQQSs3czg0ZmxtdkVPWVVycEhKdjNDSTVMaitPaFN5aXlvYllpbXpIY1dVbmZMK2cwWkxlTzdPNE5lbGxtVHNRam9TMzNsTHRDVldrYWx5c2k3MUdpMkNnNnJFQldYejZJMUhob0pqUFJXckIzcjVFTUR1bm8zNXR3dG1TWGlFbk95ZlFyL0c3cEZQMkFocmN6RDNuMkQxKytVNDZuS05JWnVPcmxmWGxwamczL2FzVDZnV2ZKZlBIaWVEdTdDcVJtQmduTjJNd1Vyc2I4V3cyMUNMM2xUb2ZyZkF4L3UxZDNqcHc5ZGxjRGVyaWpTeUtPOWI2cExhK0svQTFZMG02MGF3bHhCR3owb0tzVzM1SWZIUGFla254Z0FUT0hZQldrL29oSngvSmE0RkRwY251b1VCS1dIaXF4VVQxcW5aMmRJUG5NanU1RUVSeFlrbWNSNklXTGoyRHlUM1duejNnQWlOL2JNSXFlR3B2Y25PNkNMNFhUcmpCZ3FwbUdUSnljbHhzUS8rcW5MVUEiLCJtYWMiOiI3N2RhZTZiZTFkNWIxN2M2ZTBiYThkNGQwMzAwZmMxMDJhMTEwMDVlZjk3OTUyNDlkNjg0YzRhZmZkOGFiNjA0IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570640860\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}