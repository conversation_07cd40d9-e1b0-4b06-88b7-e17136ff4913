{"__meta": {"id": "X647f434b7fad1c7333a95847c208a17d", "datetime": "2025-06-13 08:52:18", "utime": **********.942149, "method": "POST", "uri": "/roles", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804737.418626, "end": **********.942181, "duration": 1.523555040359497, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": 1749804737.418626, "relative_start": 0, "end": **********.253217, "relative_end": **********.253217, "duration": 0.8345909118652344, "duration_str": "835ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.25324, "relative_start": 0.8346140384674072, "end": **********.942185, "relative_end": 3.814697265625e-06, "duration": 0.6889448165893555, "duration_str": "689ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46430256, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST roles", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.store", "controller": "App\\Http\\Controllers\\RoleController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=60\" onclick=\"\">app/Http/Controllers/RoleController.php:60-97</a>"}, "queries": {"nb_statements": 66, "nb_failed_statements": 0, "accumulated_duration": 0.12102000000000003, "accumulated_duration_str": "121ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3292542, "duration": 0.0052, "duration_str": "5.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 4.297}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.364472, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 4.297, "width_percent": 0.934}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.407357, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 5.231, "width_percent": 1.231}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.414279, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 6.462, "width_percent": 1.124}, {"sql": "select count(*) as aggregate from `roles` where `name` = 'Cashier2' and `created_by` = '15'", "type": "query", "params": [], "bindings": ["Cashier2", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.450233, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 7.586, "width_percent": 0.628}, {"sql": "insert into `roles` (`guard_name`, `name`, `created_by`, `updated_at`, `created_at`) values ('web', 'Cashier2', 15, '2025-06-13 08:52:18', '2025-06-13 08:52:18')", "type": "query", "params": [], "bindings": ["web", "Cashier2", "15", "2025-06-13 08:52:18", "2025-06-13 08:52:18"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 83}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.45635, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:83", "source": "app/Http/Controllers/RoleController.php:83", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=83", "ajax": false, "filename": "RoleController.php", "line": "83"}, "connection": "ty", "start_percent": 8.214, "width_percent": 2.892}, {"sql": "select * from `permissions` where `id` = '56' limit 1", "type": "query", "params": [], "bindings": ["56"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4681401, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 11.106, "width_percent": 0.702}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.4768429, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 11.808, "width_percent": 1.215}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (56, 23)", "type": "query", "params": [], "bindings": ["56", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.483945, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 13.023, "width_percent": 3.966}, {"sql": "select * from `permissions` where `id` = '57' limit 1", "type": "query", "params": [], "bindings": ["57"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4948559, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 16.989, "width_percent": 0.785}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.50082, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 17.774, "width_percent": 1.049}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (57, 23)", "type": "query", "params": [], "bindings": ["57", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.506742, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 18.823, "width_percent": 2.388}, {"sql": "select * from `permissions` where `id` = '58' limit 1", "type": "query", "params": [], "bindings": ["58"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5155902, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 21.211, "width_percent": 0.521}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.520771, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 21.732, "width_percent": 0.843}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (58, 23)", "type": "query", "params": [], "bindings": ["58", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.526611, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 22.575, "width_percent": 2.562}, {"sql": "select * from `permissions` where `id` = '59' limit 1", "type": "query", "params": [], "bindings": ["59"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.534463, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 25.136, "width_percent": 0.57}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.539644, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 25.706, "width_percent": 1.025}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (59, 23)", "type": "query", "params": [], "bindings": ["59", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.545173, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 26.731, "width_percent": 2.181}, {"sql": "select * from `permissions` where `id` = '491' limit 1", "type": "query", "params": [], "bindings": ["491"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.552931, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 28.913, "width_percent": 0.562}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5598278, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 29.474, "width_percent": 1.074}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (491, 23)", "type": "query", "params": [], "bindings": ["491", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.565162, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 30.549, "width_percent": 2.925}, {"sql": "select * from `permissions` where `id` = '492' limit 1", "type": "query", "params": [], "bindings": ["492"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5749679, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 33.474, "width_percent": 0.603}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.580735, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 34.077, "width_percent": 0.917}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (492, 23)", "type": "query", "params": [], "bindings": ["492", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.586996, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 34.994, "width_percent": 2.743}, {"sql": "select * from `permissions` where `id` = '493' limit 1", "type": "query", "params": [], "bindings": ["493"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.596643, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 37.738, "width_percent": 0.587}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6020079, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 38.324, "width_percent": 1.041}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (493, 23)", "type": "query", "params": [], "bindings": ["493", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.608714, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 39.365, "width_percent": 2.562}, {"sql": "select * from `permissions` where `id` = '495' limit 1", "type": "query", "params": [], "bindings": ["495"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6176841, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 41.927, "width_percent": 1.132}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.625257, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 43.059, "width_percent": 0.835}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (495, 23)", "type": "query", "params": [], "bindings": ["495", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6322598, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 43.894, "width_percent": 2.942}, {"sql": "select * from `permissions` where `id` = '494' limit 1", "type": "query", "params": [], "bindings": ["494"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.641547, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 46.835, "width_percent": 0.925}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6481519, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 47.761, "width_percent": 1.074}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (494, 23)", "type": "query", "params": [], "bindings": ["494", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.654664, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 48.835, "width_percent": 2.314}, {"sql": "select * from `permissions` where `id` = '496' limit 1", "type": "query", "params": [], "bindings": ["496"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.662631, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 51.149, "width_percent": 0.735}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6693559, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 51.884, "width_percent": 0.934}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (496, 23)", "type": "query", "params": [], "bindings": ["496", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6746972, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 52.818, "width_percent": 2.289}, {"sql": "select * from `permissions` where `id` = '497' limit 1", "type": "query", "params": [], "bindings": ["497"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.682901, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 55.107, "width_percent": 0.611}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.689243, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 55.718, "width_percent": 1.182}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (497, 23)", "type": "query", "params": [], "bindings": ["497", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6960728, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 56.9, "width_percent": 2.826}, {"sql": "select * from `permissions` where `id` = '498' limit 1", "type": "query", "params": [], "bindings": ["498"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.704847, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 59.726, "width_percent": 0.554}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.709717, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 60.279, "width_percent": 0.818}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (498, 23)", "type": "query", "params": [], "bindings": ["498", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.715449, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 61.097, "width_percent": 2.991}, {"sql": "select * from `permissions` where `id` = '499' limit 1", "type": "query", "params": [], "bindings": ["499"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7252948, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 64.089, "width_percent": 0.645}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.732548, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 64.733, "width_percent": 1.107}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (499, 23)", "type": "query", "params": [], "bindings": ["499", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.74082, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 65.84, "width_percent": 2.495}, {"sql": "select * from `permissions` where `id` = '523' limit 1", "type": "query", "params": [], "bindings": ["523"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.751355, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 68.336, "width_percent": 0.785}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.759223, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 69.121, "width_percent": 1.256}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (523, 23)", "type": "query", "params": [], "bindings": ["523", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7674642, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 70.377, "width_percent": 2.281}, {"sql": "select * from `permissions` where `id` = '509' limit 1", "type": "query", "params": [], "bindings": ["509"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7780972, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 72.657, "width_percent": 0.678}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.785551, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 73.335, "width_percent": 1.438}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (509, 23)", "type": "query", "params": [], "bindings": ["509", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.794588, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 74.773, "width_percent": 2.446}, {"sql": "select * from `permissions` where `id` = '531' limit 1", "type": "query", "params": [], "bindings": ["531"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.805094, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 77.219, "width_percent": 0.645}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.812521, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 77.863, "width_percent": 1.396}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (531, 23)", "type": "query", "params": [], "bindings": ["531", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8216288, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 79.26, "width_percent": 2.809}, {"sql": "select * from `permissions` where `id` = '533' limit 1", "type": "query", "params": [], "bindings": ["533"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8332918, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 82.069, "width_percent": 0.653}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.840954, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 82.722, "width_percent": 1.413}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (533, 23)", "type": "query", "params": [], "bindings": ["533", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.850096, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 84.135, "width_percent": 2.793}, {"sql": "select * from `permissions` where `id` = '534' limit 1", "type": "query", "params": [], "bindings": ["534"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.860924, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 86.928, "width_percent": 0.661}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8662832, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 87.589, "width_percent": 1.116}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (534, 23)", "type": "query", "params": [], "bindings": ["534", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.874558, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 88.704, "width_percent": 2.272}, {"sql": "select * from `permissions` where `id` = '535' limit 1", "type": "query", "params": [], "bindings": ["535"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8850532, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 90.977, "width_percent": 1.099}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.891243, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 92.076, "width_percent": 1.231}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (535, 23)", "type": "query", "params": [], "bindings": ["535", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.899581, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 93.307, "width_percent": 2.669}, {"sql": "select * from `permissions` where `id` = '532' limit 1", "type": "query", "params": [], "bindings": ["532"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9078581, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:87", "source": "app/Http/Controllers/RoleController.php:87", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=87", "ajax": false, "filename": "RoleController.php", "line": "87"}, "connection": "ty", "start_percent": 95.976, "width_percent": 0.843}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 23", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 401}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.915006, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:401", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:401", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=401", "ajax": false, "filename": "HasPermissions.php", "line": "401"}, "connection": "ty", "start_percent": 96.819, "width_percent": 0.793}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (532, 23)", "type": "query", "params": [], "bindings": ["532", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 403}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 88}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9214678, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:403", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:403", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=403", "ajax": false, "filename": "HasPermissions.php", "line": "403"}, "connection": "ty", "start_percent": 97.612, "width_percent": 2.388}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 210, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 212, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1722586309 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722586309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.425991, "xdebug_link": null}]}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "Role successfully created.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/roles", "status_code": "<pre class=sf-dump id=sf-dump-219761107 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-219761107\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1328048646 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1328048646\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1378437808 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Cashier2</span>\"\n  \"<span class=sf-dump-key>permissions</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">56</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"2 characters\">58</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"3 characters\">491</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"3 characters\">492</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"3 characters\">493</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"3 characters\">495</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"3 characters\">494</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"3 characters\">496</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"3 characters\">497</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3 characters\">498</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"3 characters\">499</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3 characters\">523</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"3 characters\">509</span>\"\n    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"3 characters\">531</span>\"\n    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"3 characters\">533</span>\"\n    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"3 characters\">534</span>\"\n    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"3 characters\">535</span>\"\n    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"3 characters\">532</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos_checkall</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378437808\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-753969119 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">513</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804688174%7C6%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJmdEFRWlJ0U3V2akVMUjJCd1hYZXc9PSIsInZhbHVlIjoiMFJwS05VbVBUT1RpZ1VBU3diOG9LRVdoNmZFc3RybUhPbG5WcXh1TG9xWWM0Um1aZkprb2YzR0twTmxiQjgwejRXUHQ1QUpSUDRTYzFoSitMdEFtSmh2RDkxVi8xYVd3Szh0aXRvb0cvY2dDeVVkRURzbXZlUUlOZ2VkeXFYOFZQMzlQU2c4dE5Ua2E0YTRhcEFrQTZ6L0xqN3ZnOE5Kekh5Qktmd01FWWduYXlTVkRtMHpuaVNkVkdubW9mOW9KdDhQTTVFa3U4VGxUUjJGTmtZM3orVmRsaFNGOUVhN3NYL2hXR2txbk51M1FUdkZuUmdzUjVTRkxZYk4xQ2w4NU5la0xjY3RlRmdjRHpRWG0yRG9Qa2FyR05DM1FXem5EMitwVDZwKytOak1idGtWTzZPNlJWT0FORTVJbThkNnpJeXFlMS82SElrNngyazRVVC9iTjZxTFpuSk1TVCtPTG1GY0s3VUx0ZytjQ3YrUktIREhmVGc2Snd2RmloVGVrOFBDM2JuUXhrQ3E0aVdRekdNNDRMLzdKOTVHckFkK3A5Z3krMEFnTTFHaXRnai9iQlhoMXEzcFhrT1dyZDh1bTEzdU1WaVFwNnlBbjhxdnkzenIrd3g1WmJSSEtEMU5haDB0MlpUNUZCcTRhR0kwVUpobWh3QmZIZWoxS0xlNU4iLCJtYWMiOiIxZTFjNjRlMGQxYmJlMzUzYWJhODFlMDc2ODdjZDdmZWMyNjVjNzZhY2NiNDE2ZjBiZWJhMzIyOWJlZmQ3MmEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikx2OEJWQ3c4aU1yaDdwMGg0K3JwTHc9PSIsInZhbHVlIjoicmI0QTRlWHFwd3lpVi91V1BiellKeXJobUhFU3JOdGZzSGU1N2tVb2RnMk9tMEtQMHJRSXhRMnRYaEkzVHgrM2o0U1ZWUmxKQmRETTQweW5TT3BodkVBTUFjckhrZWhSRjJubXhock9STTdzNHNYQWpBWVc1TU5xcUxuWE1kaVFocFFIYjFxQ3NQNTVnQ2F1UGRreUxjTjJIRnFrMFlyY1N0L2NPYzlXRWp6SWlNQ0lJcWlnZWJEeTdDODJpdjdmS045N0ZleVFzL1dZcktkdko3VnMxdTBRd2RBbGZodlkrclpuaVR4dTVaMzJuMk9GZm83aHlkOG1QeGF3NjcyWU9nSVJ5WHJ0SGFKTGZqZ3BWd1JSNDN3ZEhRb3YrWHNTdERwQXJ0WTF2WTBKNm9PMnh1NCt0WU9KQVkxVEhDVHA3MVBTb2VNRVJpc0k4clNJL0U3WTdxUGM4T2haSzU2VEdQc1l5NHFMWWY2ZThOWDVVS3pRMlBoRTQyZEViVHFodERCYVZ6VmFhVHB5NUpiTGN2dlJjMlcrR1A2OThxaHc3d0tyTDRaZTdNc0VLR3U5clFsMFJpb3pYc2E2VjRqUzVCQ01xV3hGc0ZzL2pzMmgzR2d0d0hWaTJzOTdFTWsxN2xzY3FmSkpLN1BFZWpBQUZBeUV1d242VnZQRnU1eisiLCJtYWMiOiIzYWM4ZDc3ZDdjOGU3Yzc1ZDFkNmRmMzBlNTk2M2IyOTI3ZGViN2ExZjM1OTk3YTJjYzZlZGVjOWM5ZjhlMDRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753969119\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1538031870 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538031870\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:52:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhWQWt6V0ZOVkxkOEF2NUQ3c2xBQUE9PSIsInZhbHVlIjoibUNOc0NHMTMrdEo3VktobVdHbGhtOWpoVVVwVzNQaEVJL09qRnBPZVF3TjRSeFJYZnA2MURXaUlWcFNJOThiOHEwM3JtS0ZFK2twMmhsU3d2V2taMHRINXJwZ0lGUExGTS8vYVhWUjczSkh3V1VTdkQ1UXBINHEycDNoenNHSnBoVG5xbW0wSXJVRDhZTk5iZllLZlhLdnhIdWdDZnhIOVJpUzFFd0tCK0NFNlBDRlRRL0pzRmxOaDJQWTEramw1TVJPbE1mM1g4N0pHY2tZOGpnTTdYbkJWbVpMNmUvSTMzejk4VXR2VC9KOVhZenR3am5MUmxXbHVZcVlodExZZXVuYjZFU0ZZUjd0bFJQT2pRSmxqdlNMUlFsZzdjTytVR3pCT0M4OFAzNDVaNEJQaVpKOGdzU0w4QW4xZFBKUGxNVUV3dkFQSHlNblVhWlVvZDRzSzFCaTlneVZWSTBkVEhvU3dCbDRYQWQ5bDlHd3orTWk5NEh2aVBIdFhxdmkveUdCSEo4dW52bzVqbkdVUUVaY1R5SWZ4cW45TkRxc2x5Rkl3UzNLVG85VEJLeDdySGViR2N6SHptb2hXUUIxMHZZZVpIdzFsbU1qL2RybWtGZmNOMTBRY0lmMTRhcWdxQkdtUllHOERFbk1EekppRjIwNGtXalVKciswMHYzSFkiLCJtYWMiOiIyMjg2YTdhOWY5NzE1ZTNkMmUyYTE5NDg0NzljMjI3ZjQwMzliY2FjNmQwNGJmNWUzMTYwOWI5M2E1NzgzZjAyIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:52:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlprczVPR1ljRWhoNkNCVWtGcW1DTXc9PSIsInZhbHVlIjoiVW5QZ2xtSjcwOEhhSjRIemx5ZjU3UVhMM0lTcitFRTlFM1JCWDVUbXF2emdwZ3E0Rk11YkFVUUw0RmJsWSt6aFYwN3I4L012QXg3NnkzMzRMY1ZDczU3RTZCUTkyakVQY1lpUW96cCtjM1RNRjdqNjlLY3RieTRHSisxSDhLck9QaUhWa3dPUmYrMXEyYmQ5dCs0anhsZitSL2hiaVZMSUhjY0FkV0U2U0IrNkFPQWNHbFA2MWZXbGoyVHl0R2U0bVdJeGd6ako2WmNKeW9hN1VJS29RenhjVjRqM1NqbzZHajRmdjQ0L0dtN0RoU3RaM1BUUWUzZEpVQm41dVIydGZlYmErUEpCbC9Gd0tQQlo5K3dPQjdMcFFCZ2lPdGMveE50UlJWdDZDMElDbXZHNm1rU3VkMXl1RzlFTVpRTWpmbDg5ajY4clIwdXNrcjlZQS9ZaHlrQzBnWlBEdngzb2x3RlVFRjV4OXlmT2ZsdTFpVjM0b0hpRExpOGo1SjRoOVhaVWdQdXBPRjdhMkV5UDJxb0UzR1pkQkcyN0ZaRW1MNzBUd0p6NUNJZjBPMzh2eFdTMnhhV3d5dUJJVGFaa2RhYTRrY2EybGRuZGxOdEdkdFo2bzQxUUdiNTZsK2N1RUlJYWo3NGJNT2N4cSszbDQzSUU2ckZMQ3BMQmE3WXEiLCJtYWMiOiJiYTU5MmEzN2FkYjQ5MTliM2Q5NjY3NmQ4ZTFiMjE4Y2QxZWRiNWY2OGNhYzE1ZDkzYmVlM2Q3NzUyY2ZhODA0IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:52:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhWQWt6V0ZOVkxkOEF2NUQ3c2xBQUE9PSIsInZhbHVlIjoibUNOc0NHMTMrdEo3VktobVdHbGhtOWpoVVVwVzNQaEVJL09qRnBPZVF3TjRSeFJYZnA2MURXaUlWcFNJOThiOHEwM3JtS0ZFK2twMmhsU3d2V2taMHRINXJwZ0lGUExGTS8vYVhWUjczSkh3V1VTdkQ1UXBINHEycDNoenNHSnBoVG5xbW0wSXJVRDhZTk5iZllLZlhLdnhIdWdDZnhIOVJpUzFFd0tCK0NFNlBDRlRRL0pzRmxOaDJQWTEramw1TVJPbE1mM1g4N0pHY2tZOGpnTTdYbkJWbVpMNmUvSTMzejk4VXR2VC9KOVhZenR3am5MUmxXbHVZcVlodExZZXVuYjZFU0ZZUjd0bFJQT2pRSmxqdlNMUlFsZzdjTytVR3pCT0M4OFAzNDVaNEJQaVpKOGdzU0w4QW4xZFBKUGxNVUV3dkFQSHlNblVhWlVvZDRzSzFCaTlneVZWSTBkVEhvU3dCbDRYQWQ5bDlHd3orTWk5NEh2aVBIdFhxdmkveUdCSEo4dW52bzVqbkdVUUVaY1R5SWZ4cW45TkRxc2x5Rkl3UzNLVG85VEJLeDdySGViR2N6SHptb2hXUUIxMHZZZVpIdzFsbU1qL2RybWtGZmNOMTBRY0lmMTRhcWdxQkdtUllHOERFbk1EekppRjIwNGtXalVKciswMHYzSFkiLCJtYWMiOiIyMjg2YTdhOWY5NzE1ZTNkMmUyYTE5NDg0NzljMjI3ZjQwMzliY2FjNmQwNGJmNWUzMTYwOWI5M2E1NzgzZjAyIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:52:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlprczVPR1ljRWhoNkNCVWtGcW1DTXc9PSIsInZhbHVlIjoiVW5QZ2xtSjcwOEhhSjRIemx5ZjU3UVhMM0lTcitFRTlFM1JCWDVUbXF2emdwZ3E0Rk11YkFVUUw0RmJsWSt6aFYwN3I4L012QXg3NnkzMzRMY1ZDczU3RTZCUTkyakVQY1lpUW96cCtjM1RNRjdqNjlLY3RieTRHSisxSDhLck9QaUhWa3dPUmYrMXEyYmQ5dCs0anhsZitSL2hiaVZMSUhjY0FkV0U2U0IrNkFPQWNHbFA2MWZXbGoyVHl0R2U0bVdJeGd6ako2WmNKeW9hN1VJS29RenhjVjRqM1NqbzZHajRmdjQ0L0dtN0RoU3RaM1BUUWUzZEpVQm41dVIydGZlYmErUEpCbC9Gd0tQQlo5K3dPQjdMcFFCZ2lPdGMveE50UlJWdDZDMElDbXZHNm1rU3VkMXl1RzlFTVpRTWpmbDg5ajY4clIwdXNrcjlZQS9ZaHlrQzBnWlBEdngzb2x3RlVFRjV4OXlmT2ZsdTFpVjM0b0hpRExpOGo1SjRoOVhaVWdQdXBPRjdhMkV5UDJxb0UzR1pkQkcyN0ZaRW1MNzBUd0p6NUNJZjBPMzh2eFdTMnhhV3d5dUJJVGFaa2RhYTRrY2EybGRuZGxOdEdkdFo2bzQxUUdiNTZsK2N1RUlJYWo3NGJNT2N4cSszbDQzSUU2ckZMQ3BMQmE3WXEiLCJtYWMiOiJiYTU5MmEzN2FkYjQ5MTliM2Q5NjY3NmQ4ZTFiMjE4Y2QxZWRiNWY2OGNhYzE1ZDkzYmVlM2Q3NzUyY2ZhODA0IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:52:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-560580649 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Role successfully created.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560580649\", {\"maxDepth\":0})</script>\n"}}