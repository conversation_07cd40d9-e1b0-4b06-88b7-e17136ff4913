{"__meta": {"id": "X6d1cdfc5355499df372cd42ca1a7ebc8", "datetime": "2025-06-13 08:52:22", "utime": **********.421599, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804741.255541, "end": **********.421636, "duration": 1.1660950183868408, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1749804741.255541, "relative_start": 0, "end": **********.252752, "relative_end": **********.252752, "duration": 0.9972109794616699, "duration_str": "997ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.252782, "relative_start": 0.9972410202026367, "end": **********.421641, "relative_end": 5.0067901611328125e-06, "duration": 0.16885900497436523, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44183216, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02739, "accumulated_duration_str": "27.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3258889, "duration": 0.02501, "duration_str": "25.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.311}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.373474, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.311, "width_percent": 3.468}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3960989, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.779, "width_percent": 5.221}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-650230159 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-650230159\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1832167639 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1832167639\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-129738168 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129738168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1232656562 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804688174%7C6%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM4MzdiMzJ6WElXMm93N0lqTjhBVWc9PSIsInZhbHVlIjoieHNQVGtJREVXRUpVSjRrY3ZRai9jODVDOHlOMVRoQW9hSFhlSUNnMnpHbE1GSFg4V3REejczV0tEMkpLOEZhVWJCSlFNVzZmK1JPTDhDcEt0QmpmVU1qSXYzRzY4SDVCd2dYazgzVkt0a2hBU1FZSnpoaDNFTGs5bStnQkN1a01uOE1JVzJ4elJiNjJzMlRiSklEQXNqcGlLWFNmdnJYeHcwdzVRd3FYSmM5YlpOZXZBNGhnaDNNeEwxMHJHTXpxY1lzYnVNbDZOY2xZREY3ZEdYVXBmbzVUKzhobDZqL216YmQyeHZkcUlNRWFKSnFjdFRWVnE5NlV2QkZDUHcxVjUzc2IxemYvQmNHMnQySnc2UjdHMG9pVUIzbTRVd0FmSGFjWG5maWVaeS8wWTB6N0dnQllabDd0QXJCWnQyZHA5WnJLdUUwRC80M2laZnhWUURvZVc1dUZrRXNUNzJkUjdydDdoY054aGY0WUJ3U0N1TkEzaTRYejFPL1Y2WXJKMG9PS2ZXL1lDWGtuYS9rUjJnU0JzODhVNmVZQ2pGdFA4V1AvbTVhV241dlozTWYxV0ZBS2JqQmx4Zy9saTBlU25nM1U3RHhXL0h4V1VXQ1owZ3FwM2t2MjBybzdtUXBlTXovRkQ5RmM4Uk1aaGtYSWF2Ry9Cb0dJQXFVcGdkYlYiLCJtYWMiOiJiOTg2MDBhNjcwMjU0MDM2ZGZiZDYyNzA0ZGVlNzRkMTE5YTY1YzNkMmMzOTJkZDdjZGFmZDMwZDFjZjFhOGNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdjcG9FUEZ5Mi83VWlJeE1tdDhUb1E9PSIsInZhbHVlIjoidjFMalZPV0JYTEdKajd2Mll6QVZmOUtlWkd4K0dFZ0ltUjQ1Wks4VVFFSXdOakh5M1VHbnZFbU9NUU9FL2pCaGN2a3pMZGQzNFpUOGx5R1hHK25Ib21USzhuaDVueW9hcWRraitOQnVjQmg3M1dVbmJ3QXk5YmNSWmsrUlA3cnFlcllsNFMrTHZ5NVI5UG5yeXAwT0R4d3QrYVNudHZnbURIZ0tPOC9ZbUZESW5XanRnQ0hQSVREVTlNcmlidU1LU2xsVjQxQzRzU1BIcHo4L1BNeUZINXhFWGFyODBiR1hXVHZPUFpnWHVxbmQ0MXp1T1NlcXk2c1FEeWRxQTRJT2RJMmVkRUhsRE9XSGQwRHFMd3Q3azI3RjVHbUhsbWlRZUM1c0ovMzFIMUFaaEYrdU54N2UrT1RUUTRQWTlTWjJiYWRJYjVMbk1sMWp2WUtIeXlXSUdyV0Y0TFF3QlRzeXBpMDFRb1UxbzF2U1R6V1pRa3RQSEgrMVpZL3VEWGp3alpwVTh5MElJQjV3L3F4cEdjRTB2bG1USm9FWG8yczJTOWRYbzRraUt1ZlphQ3dvZS9qci9vRnphUnRLTWVia2JjWklQZVFkZjRldmg5OXdOSFFGYWUxaUNSV0NpYitiKzgzeVFPWFpwMG53ajV5ZDNNMDNKRzNjOEVUQ0ZZQUIiLCJtYWMiOiI2OTY5ZGE2YzJkZThlNjE0ZjFmNGEyZTkyZGNiOThiODQ2ZDU3ZWY4ODhmNTA4YzFmNWNjOWFiYTVjYmY5YTM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232656562\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1550843072 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550843072\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1567459278 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:52:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IksyUlFIZVQ2YjNBSUtlN1N4VDVqQlE9PSIsInZhbHVlIjoiRmdzZlRKZVFmTGpoZFBoYlIxRTNJeksybWRKeE1zbVdmUFNUQUlPTzRBS3JKWmM2UTRyZEhHL3liU1lUSy9lYWZKK1BsckJ0cEpOYVNiRGN2UDJTc0Q4VXNtMHlQRGk5V3RHSndWcHJvSjNCdFhIWTA3SU9JN1ZjYzhRVEtyUnhyNHdaQU14UzEwNkhzak1OcjBKWXd4ZjByRWRZUTJiRk1wZGhwSUlkb3NXN3dQR2J5dFFvV1JDV2lyWDFMODA1aTBvSVZacklCNnEyMXMveXBPSWZHSEJRRXZqcXFUb3Q4YXFzUmRETmtXMHBReGF0emZXNzJJNTdKSEVQUDZJR1ZZak1Ec0RSaCtOUTRKUHd6dEowb0RMVFE4L1Y2NzNtQis2Qko5Z0p6SHA4a3F3QWp4ekxSZWpFb0I5cGFJTWVwYjBWL3NsWlhNTHRFemRyWnpCNUZocG1TTTdFbEwrTzkxSkYxcW41dUZCMWd0RnRYOTdUU25McFlSd3lZR05vOTNyZWRrOUZNQkU3ZHlTaHZUN3lIZ1J5c2txa1Q4T0VyNTF4V0d0Q3VyZlpxSWtwd00yMHJqbUpHWGhFbHJ3bGt4bVVtSHJjRm5POFdBVVhMdHFiY3hPL2E2bFE1QUxDYkNocStTUTdCaHJtUjQwbTVXaENrNW8vRklPTTVGS04iLCJtYWMiOiIzYWZlNDM5OWM5Nzg4N2FkNzQ4NWJjM2MzODQ4YzFjN2Q4MGFmNzdjYTdkNjRlNTNlYmIzYzZiMGYwNzZlNzA0IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:52:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNJNXJpVk8rRE0xZ2xZNUE3aHZLY2c9PSIsInZhbHVlIjoiRlFOTE5HKzRPYTYxNzA0VC9jTGt4UjJVMnNpR1Nub2svSjcySEkxUTNsYzQwQ0tQelFEMWN6c1NuSkp0blBja1FHQWM0WkpGNFJDck81Mlo2bXRyd0owOVFKUHJpRW5yaGRtNzM5TlpyajRuTGlpbytxNEFSOURDKy9nWGVBeGd2MGhWdXorT2wzZ2h4bnRTMGtSaEEzbjdUOXlXSWNzaklsOW9pYTVYcVFjNDlYbDBzdUgzeGxpblVoVUF3K1RIT0ZxUnduM2wyTm5TRExpMDVPNkJIWTNiNzA4MnlHRUJtU1JDbXFCY29XOTJla0d6dzdwL1RkYmlYbzJaQkVzTHFkaStsNDJNb1BxZ2UyMjdKb3A5N2M4MEhnM1Vnb1VhVVVLVmQvdis2Z0RFYzBJa0ZQWFYwZXR0b1dyb01rS3pMNlN3Zkw3Rlp3QmdOcHRmV0pPcjBOck1BdzVSWEEyQzl5TlJ5TFlxMFlYMUQwSUJmYllwTm5ROWNTcWVlblNlUWdTbXhGS0xLK1p3ZmZaT2RRRTZteDNRekQwaHg2QWdJNCtFUDFvck82ZFU1WVdpcE1sNUIzemtFTDcxVVZpZStGaXA3WWFCMFJGazQ4bEZja2EreDRxT1dYWS9BR3UxL0pTcHlZcnV5NzAxaE1XVmRZRjVvMnA1SnV1OG5BRVIiLCJtYWMiOiJiNDdjN2U3ZmM2ZThmYzk0YWViNTUwOWE3ZWM5YzEzNjhhOTUwNmNiOGMyM2U0ZDM1YzUxODcyNmZhMmYwNTYxIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:52:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IksyUlFIZVQ2YjNBSUtlN1N4VDVqQlE9PSIsInZhbHVlIjoiRmdzZlRKZVFmTGpoZFBoYlIxRTNJeksybWRKeE1zbVdmUFNUQUlPTzRBS3JKWmM2UTRyZEhHL3liU1lUSy9lYWZKK1BsckJ0cEpOYVNiRGN2UDJTc0Q4VXNtMHlQRGk5V3RHSndWcHJvSjNCdFhIWTA3SU9JN1ZjYzhRVEtyUnhyNHdaQU14UzEwNkhzak1OcjBKWXd4ZjByRWRZUTJiRk1wZGhwSUlkb3NXN3dQR2J5dFFvV1JDV2lyWDFMODA1aTBvSVZacklCNnEyMXMveXBPSWZHSEJRRXZqcXFUb3Q4YXFzUmRETmtXMHBReGF0emZXNzJJNTdKSEVQUDZJR1ZZak1Ec0RSaCtOUTRKUHd6dEowb0RMVFE4L1Y2NzNtQis2Qko5Z0p6SHA4a3F3QWp4ekxSZWpFb0I5cGFJTWVwYjBWL3NsWlhNTHRFemRyWnpCNUZocG1TTTdFbEwrTzkxSkYxcW41dUZCMWd0RnRYOTdUU25McFlSd3lZR05vOTNyZWRrOUZNQkU3ZHlTaHZUN3lIZ1J5c2txa1Q4T0VyNTF4V0d0Q3VyZlpxSWtwd00yMHJqbUpHWGhFbHJ3bGt4bVVtSHJjRm5POFdBVVhMdHFiY3hPL2E2bFE1QUxDYkNocStTUTdCaHJtUjQwbTVXaENrNW8vRklPTTVGS04iLCJtYWMiOiIzYWZlNDM5OWM5Nzg4N2FkNzQ4NWJjM2MzODQ4YzFjN2Q4MGFmNzdjYTdkNjRlNTNlYmIzYzZiMGYwNzZlNzA0IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:52:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNJNXJpVk8rRE0xZ2xZNUE3aHZLY2c9PSIsInZhbHVlIjoiRlFOTE5HKzRPYTYxNzA0VC9jTGt4UjJVMnNpR1Nub2svSjcySEkxUTNsYzQwQ0tQelFEMWN6c1NuSkp0blBja1FHQWM0WkpGNFJDck81Mlo2bXRyd0owOVFKUHJpRW5yaGRtNzM5TlpyajRuTGlpbytxNEFSOURDKy9nWGVBeGd2MGhWdXorT2wzZ2h4bnRTMGtSaEEzbjdUOXlXSWNzaklsOW9pYTVYcVFjNDlYbDBzdUgzeGxpblVoVUF3K1RIT0ZxUnduM2wyTm5TRExpMDVPNkJIWTNiNzA4MnlHRUJtU1JDbXFCY29XOTJla0d6dzdwL1RkYmlYbzJaQkVzTHFkaStsNDJNb1BxZ2UyMjdKb3A5N2M4MEhnM1Vnb1VhVVVLVmQvdis2Z0RFYzBJa0ZQWFYwZXR0b1dyb01rS3pMNlN3Zkw3Rlp3QmdOcHRmV0pPcjBOck1BdzVSWEEyQzl5TlJ5TFlxMFlYMUQwSUJmYllwTm5ROWNTcWVlblNlUWdTbXhGS0xLK1p3ZmZaT2RRRTZteDNRekQwaHg2QWdJNCtFUDFvck82ZFU1WVdpcE1sNUIzemtFTDcxVVZpZStGaXA3WWFCMFJGazQ4bEZja2EreDRxT1dYWS9BR3UxL0pTcHlZcnV5NzAxaE1XVmRZRjVvMnA1SnV1OG5BRVIiLCJtYWMiOiJiNDdjN2U3ZmM2ZThmYzk0YWViNTUwOWE3ZWM5YzEzNjhhOTUwNmNiOGMyM2U0ZDM1YzUxODcyNmZhMmYwNTYxIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:52:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567459278\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1975699238 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1975699238\", {\"maxDepth\":0})</script>\n"}}