{"__meta": {"id": "X7567dedda237925f80d4682dd720180e", "datetime": "2025-06-13 08:50:28", "utime": **********.04836, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.214584, "end": **********.048407, "duration": 0.****************, "duration_str": "834ms", "measures": [{"label": "Booting", "start": **********.214584, "relative_start": 0, "end": **********.879539, "relative_end": **********.879539, "duration": 0.****************, "duration_str": "665ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.879568, "relative_start": 0.****************, "end": **********.048412, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027910000000000004, "accumulated_duration_str": "27.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.941772, "duration": 0.02067, "duration_str": "20.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 74.059}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.977859, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 74.059, "width_percent": 3.225}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.019761, "duration": 0.00634, "duration_str": "6.34ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 77.284, "width_percent": 22.716}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804607060%7C1%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhGTlFVdW0rZkhYLzlXaXZ0L3dJbkE9PSIsInZhbHVlIjoiRzhObElKeHNKaEExZmFWRVkxMzkvSlJUeTNuK1dEbGx4VWJwRmhkOUNabXZCRXh4VG5iZExQWDNIbllMNWo5UHhXVWovdHFvMGxQSjlhRFp0MWFYaUw1WTNNQVBZeTVZcjd5Q3FxWERHbGx4SzlzU1l6c1l5UkUrQXhoU1Y4byt2U2MwdWhrSDI0bEpuQWZvSHlBYXRmdEtpYmU3MWoyZWN4L0NNQXIxVVRvWTZaZ3NLSnlFOE5mb0lWd1kxc1BXYXhQS01uejZoSmZCWjF6aE9sc2U0SUZhNFVVWkVVanZHQ3NUOE9PTWo5Um9pQ2RlUUVzM202RUI2Z0xUNy95clUvNWpPUFpJcGtsZkRiSFlnZGJ5SUFuVTZrMXM5bXVqWUFVdHFYR21zTTJLOXRhNGt6b2duK0lHaVdmS3NZQzNwZVlFeit5ZVZmZXdsUGZzQ3VvVjRob0Z4ZHdhcUZLWVd2V3hCclRTQVM0MTNqM29PVkw5N2JGREpwaERVeExKRk1Dc25xYTZDcm5kS2NsdEJ0S1ZlR3BSeUY3eXJjV2t5MnZmaExHblFZbEhxM2lNd2NUMldCNWtaWlJVTjdpQ1JnSTk5S2xCc0FxMmhKVXI2VVFERUgvUUdxTk9ScndoNGhMRnRCOXY0RWFySHNPVGV2WFNzOENDSGdHWGRHcWciLCJtYWMiOiI0MWZmYWI3N2FkZjRkYTE4N2JjMzViOTQzNWQxYWM5NGYzYWE5NzNhN2ZjNDgyNjdmM2QzODU3NDM4YTNkY2U1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdiZ3VrUlVXM3dnOEN2bitTRVJCSlE9PSIsInZhbHVlIjoiRVhqL0g5S0dldHpnS2FCa0w0TTBrKzgrUWE3VmxzbnE5WFBOS2NhU3d4eXIzVUVuVTlJUFQ1YnAzaVRGZktKOXZrbFAvalJCM1lmZFhrQmZzT05RaW15T0IwM1NpYXBQeW1KejRkR01NN3AvaEw3QzlvWmE4MUJmOWF3Y0h4MDA1TU5mbTVRZ001Sy9iMlZrVWxlVXo0Q3l1MlVxME4vOStLV0FpS0FaY2VIZmJIcHZ0YkNUdFZQT3kxYitkRXhnNmFuYmxBSittNTF3ZTVDRWM3RXBOVkMvS0J1a0FnbE9XQklick1EcTlCMVAvVEVGa1dpT09MWnRlWENHNHNWTXpBdFU3OGdNbWJWd2RaeWlldFF4TERHVGxLamNJa3pBUTJ1NkxFNWNOWGRjZ3I4SzduRnVyWkxyRVY0MU1YRDExZkFSOTd4cDlzdEpBTUZKZEVPRmNtY0xtQVVFVzlYTE1VdDNraHFvRnZtMGlkS0JCOG93eC9RMzJTaDhxRzdSMXF3VEpqT2pHQ3I5WFNBOWIvNk84eG4ycVRiZkk5c1Q0Z0ZCOWFybi9BbVZFTEJVWXBOMW5WUHpDOHpBdlhSNk90eFFZdU5oTk1leFlINXE0dEVUUEFvdG0vK3ArYy9peVlPNXhBdzZqKzRmZmRlSDByUEphZzdaQ0xsakh4SEciLCJtYWMiOiJlZDgzNzUzMmNlZThkZWZmODRkZWY3ODVkZDAyZDgxYjMxYmU0MmVmMTAwYWI0NzJkZTdkNTgwNzY2M2JhOWMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1538543864 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538543864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1143748495 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:50:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZDdTlDS0hNUWZXL1JKZlAyWEJEa3c9PSIsInZhbHVlIjoiN2NrKzVZVHE1QSt2dHdQUU5CajNLYWtVSlRkZWNUMlFwcnh3SUc3WCtVYW56NEc1c01rZDlIOW5TU1duUUZWN1JPWWw5dXY5bE9YUW5vVGk4TFB1ZjNQb2NEQ2J0RWNHd2dLanRUNzlnUXYzOTMxb0tYeVNWVHhyU2dzOTVSUy9ubnRJbTl0aTR5TzFIT3daY1hEYzdXZm1MUkVVNEtJYWJpSnVBaTB0dEVlYnNDamhobmNybEk0KzJXT0x1TDlLSWRiRUVkYXN5NHZXaFNxTU54VXAwemh4dmo1cWljRkw4c0lRaGJLS3BiSU9FSEZaYW4rek9jWit5WUpvMkRsL3h5N1dJM0c2RWlTdXZjYmFvOVJ6Yi8vN1JZQXhKdk52bWRVZ3QxRC96UzR0Vi9lQ1RUQUh6TXF5bkFIMGlVdjliZXU1R0pPYWpHRkh6aVY3WU5GVHRFZzJoajNoTGJYcytrVk9iL3NMaGxrcWVBZGVKcUVEaG9lcGhnalBCMFFiLzA4SEw5MEEvSlkwVWczT3haVHZ5MjBkV25SeEdnVStUUE51Nlg1QStVSE5MdUZsbFkyWWUydmdIbHp4ZC9HZXZSbEdPcXNrd3FvbDNZZWZrVklVU211VVZLUFdSWWh6YkUrVTRUbzUxSmFDeWFTYTZJYzJrNFpTb1c2N2JITFoiLCJtYWMiOiIwNGU5OTBiNjZlODZhNTc0YTYxOTMxNGQyNDNmMGE1NDcxNjc2YTlmZjRlYTUyMzFlYWM4NTAxMWUzZWUyZjkwIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFRUW9IUWpPRFhXQmczT0hqakQ0UWc9PSIsInZhbHVlIjoiUEtqMHJIdjF1VC9oUDgwcEw2QzlMT3Y3ZEFiTmZkRWNwdXRoanNsSDBYa1FKY2VTVk1tSGxicE0vRHFSRGZKMUtzM0t5OEJuZTNLVXZsMGVvR0FYdkZnNVA0V0xQT0JYY3ZWaHV0MTkyTXRIbFR4dGhCV0M1RS9wVUt2TUdzZE9OTGxQd04vTld1amJWYkNaR0hqWmMwYzhRMWVzaW9QTVBKZmpvTVVRNk5ERWJqakQzMWlSbFpUUWQrWWJGZmhkaDFRZ3llb1VsZnE0N0QvOHJXTW5ER0ZvNHJvcUZWVEJpUFNycjdqcW85WHlKd2t2UlFTM3ZpZWJ5LzY3NURkNllYNXB2Smk1K3B6dUZVUExKdTNxU24xdnp3SmUwNW5VQnpoaVI4OUJTaVJVU3lZWkNnUnVCYWI4ZVF3R2tEVjMxdEkxSm41T1NSSkp6UEtOYVBFMWZkd1JJY3kvNmN6cEVKWmQxRmdPLzNQZHhtTnJnek9jNkdWR1dFaDRSVDN3R3JvakI4UUVaNmJFMy9KS01kVUFpZm5hdVZIL25qZDNweHFxaTNzdjBoUUVobk95N1lITXNjcTBIZUxWMDZEczhNL2RmNEtWUmhvYnYzRnJJTkFMNDg0OC8zUCtBRjA2SjN3TStaaFM4aTM0aE9OSVBUUm5qN3N1Z2JUbzVLZXMiLCJtYWMiOiJiYmVjYTdjZDcwNjE5ZTY3MmRmODkyZjI4YzVlYmU3ZGVmZmMzMWFlYjJlN2FhOGE3Mzc5Mzc2NzNlMjUxY2ZlIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZDdTlDS0hNUWZXL1JKZlAyWEJEa3c9PSIsInZhbHVlIjoiN2NrKzVZVHE1QSt2dHdQUU5CajNLYWtVSlRkZWNUMlFwcnh3SUc3WCtVYW56NEc1c01rZDlIOW5TU1duUUZWN1JPWWw5dXY5bE9YUW5vVGk4TFB1ZjNQb2NEQ2J0RWNHd2dLanRUNzlnUXYzOTMxb0tYeVNWVHhyU2dzOTVSUy9ubnRJbTl0aTR5TzFIT3daY1hEYzdXZm1MUkVVNEtJYWJpSnVBaTB0dEVlYnNDamhobmNybEk0KzJXT0x1TDlLSWRiRUVkYXN5NHZXaFNxTU54VXAwemh4dmo1cWljRkw4c0lRaGJLS3BiSU9FSEZaYW4rek9jWit5WUpvMkRsL3h5N1dJM0c2RWlTdXZjYmFvOVJ6Yi8vN1JZQXhKdk52bWRVZ3QxRC96UzR0Vi9lQ1RUQUh6TXF5bkFIMGlVdjliZXU1R0pPYWpHRkh6aVY3WU5GVHRFZzJoajNoTGJYcytrVk9iL3NMaGxrcWVBZGVKcUVEaG9lcGhnalBCMFFiLzA4SEw5MEEvSlkwVWczT3haVHZ5MjBkV25SeEdnVStUUE51Nlg1QStVSE5MdUZsbFkyWWUydmdIbHp4ZC9HZXZSbEdPcXNrd3FvbDNZZWZrVklVU211VVZLUFdSWWh6YkUrVTRUbzUxSmFDeWFTYTZJYzJrNFpTb1c2N2JITFoiLCJtYWMiOiIwNGU5OTBiNjZlODZhNTc0YTYxOTMxNGQyNDNmMGE1NDcxNjc2YTlmZjRlYTUyMzFlYWM4NTAxMWUzZWUyZjkwIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFRUW9IUWpPRFhXQmczT0hqakQ0UWc9PSIsInZhbHVlIjoiUEtqMHJIdjF1VC9oUDgwcEw2QzlMT3Y3ZEFiTmZkRWNwdXRoanNsSDBYa1FKY2VTVk1tSGxicE0vRHFSRGZKMUtzM0t5OEJuZTNLVXZsMGVvR0FYdkZnNVA0V0xQT0JYY3ZWaHV0MTkyTXRIbFR4dGhCV0M1RS9wVUt2TUdzZE9OTGxQd04vTld1amJWYkNaR0hqWmMwYzhRMWVzaW9QTVBKZmpvTVVRNk5ERWJqakQzMWlSbFpUUWQrWWJGZmhkaDFRZ3llb1VsZnE0N0QvOHJXTW5ER0ZvNHJvcUZWVEJpUFNycjdqcW85WHlKd2t2UlFTM3ZpZWJ5LzY3NURkNllYNXB2Smk1K3B6dUZVUExKdTNxU24xdnp3SmUwNW5VQnpoaVI4OUJTaVJVU3lZWkNnUnVCYWI4ZVF3R2tEVjMxdEkxSm41T1NSSkp6UEtOYVBFMWZkd1JJY3kvNmN6cEVKWmQxRmdPLzNQZHhtTnJnek9jNkdWR1dFaDRSVDN3R3JvakI4UUVaNmJFMy9KS01kVUFpZm5hdVZIL25qZDNweHFxaTNzdjBoUUVobk95N1lITXNjcTBIZUxWMDZEczhNL2RmNEtWUmhvYnYzRnJJTkFMNDg0OC8zUCtBRjA2SjN3TStaaFM4aTM0aE9OSVBUUm5qN3N1Z2JUbzVLZXMiLCJtYWMiOiJiYmVjYTdjZDcwNjE5ZTY3MmRmODkyZjI4YzVlYmU3ZGVmZmMzMWFlYjJlN2FhOGE3Mzc5Mzc2NzNlMjUxY2ZlIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143748495\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1******** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1********\", {\"maxDepth\":0})</script>\n"}}