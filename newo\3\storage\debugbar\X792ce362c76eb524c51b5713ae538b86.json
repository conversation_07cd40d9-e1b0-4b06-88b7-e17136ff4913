{"__meta": {"id": "X792ce362c76eb524c51b5713ae538b86", "datetime": "2025-06-13 08:50:32", "utime": **********.618405, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.800086, "end": **********.618427, "duration": 0.****************, "duration_str": "818ms", "measures": [{"label": "Booting", "start": **********.800086, "relative_start": 0, "end": **********.468101, "relative_end": **********.468101, "duration": 0.****************, "duration_str": "668ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.468119, "relative_start": 0.****************, "end": **********.618429, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02783, "accumulated_duration_str": "27.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.524723, "duration": 0.0251, "duration_str": "25.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.19}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.565561, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.19, "width_percent": 4.348}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.599049, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 94.538, "width_percent": 5.462}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C**********255%7C2%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJzMEt4K3pjZ1NYTU1oR0xVaVkxaWc9PSIsInZhbHVlIjoibDMyRTFXT0tIWlh3RUNaZWt3UVFUZmxnT2M2eEtDRHdLaENuM3MwQ2UySTF1N3FMRUk1cVkvc0ZuVDhCSVVlWFVFMWl5RFhYZ3BiVU9yc1hjWk1neXBuMVhEakpYamZwMEUwbVQzWU5WVTdrRDRjWXF6WFpDdkJsbDBlNlFOTDFKWDJmWHdKaGMydERDUkNzeW9MT2tLaVV2UFR1RlFmTUh1MUptSEdUcVpNQkFMU25kUHpJY0VFOVI0YVowUnR2cGUreWYrTHEvcUlKaCtwQlFSNnJUMjJZT1hVSEJ0OWNrd2FobW1kR2ltNHFWTGkzcWx1dk5EOFh4Ykx5YmUxQVpVVUN0Zk92TXFaMG5ORlpCL3JqNUNJODRSTU1kVkkwbGc3RDl4MEUxVTl2bGJsNW9HZkIxODZXWkgrNk1wMGJPM2szY0pESHE1WE42NDFjc2VaajFCUHMxZUZSTGM5WW40YnlaczVzY2ZaSW1jV3pFV3FXN3RnYkFtcGJ2L0Nxd3lqSi83RjRpN3p5N0pqUGF6L3gyNVRXckhKZWRFVS9KV0x5QlhOUytaMXBJSmpUQVh0Ny9uVHFkclhyUXZaTWphQXJIMG1aYVJENTdTSzFmZnVHYVRuWTZNTTNrc3dqcU40L0M0NXJTSWZsNjJuYmtUNVZ0MkhCeWx5WkIrVmYiLCJtYWMiOiJjMGZmYzkxZGVlYTFmOWUzNjY2ZjEwMGRkNjRjZWQ3NDhmYzMxNjM4MDM1ODlhZGI4MDNiYTUzZjE5ZWEzMTRmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVObDVFclB4WE1xNWEvOHVrQVd5VXc9PSIsInZhbHVlIjoiVmx1Vjc1RFFRTjJEWnpOTXhEZnlubHlEVGRlVUNHWXRocUgzZU9SaVp6bm5PTFcrMTcwTUlhazh3cGlCaUM2alh4aFprVk9xWC9vWWhxQkRuMm1NUVVUbmtld0FIQktjODZsY3NEc2gwdi8zL0ViRDFqbkY2YnBKZC8weDF6eUFEb05WeGdxVGR1NWVtNFVFTFEvaWc2UTdMYTIyRU1oTUprWEhzWUFheCtmZGdqcnhSTDlZbDRwL1Zadzc1K2RqTHova2xaUis4cjZ0OWpvLzE1cHpDOWcvUHBnZEFHVFBQVzcxenpBUVdReEsxOXBzWG8vbUdMUk9aZXNodlZCRko4bjlScFhLa3pNTzc0Y1FlWFM1VkZmS2RLdmZDQzc5VEkxNXcrUFh4eHprRmEzTHhackRWb1Rpa2laVlk5b2pTcC95aWFyZmlnTXgzUDVmMVpiRHNXbmw1SmI4dDNSc0FKSTk2d000TFNuWW1QUWlmcVhnd245TTJ5dko1ZDVnNmpUQlRlT3VFSXkwbnplNlBHeWlTRnM4dHZzUWlWcjkxdzFRd1hFNkpVTmhSRWVCQVpDT0Z4dzRONjB4eUo4TXkvQloxZk4vWFZjeGJLZ1RwWDNXeVY1MXNBcFMwUFREUldmTnZUS2FnY0lJNXRMTk1XYU1McnZTbjQ0Vk1lZkkiLCJtYWMiOiIwMjQyZTBhZTIzMzdjNGM0OTZiMjcwYzg2NTc0Zjg4MTY2OTkzODM1ZjljNDgwZWVlZjRmMThkNzlmNjc3ODgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1733686586 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:50:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Img1Y2pRd0s2dFFyVkgxTmNoUktHM3c9PSIsInZhbHVlIjoiNDBBVm9xMFBTOWdOYjkyRmZpL0JpdVBRR0EzVDZRVTRoOVQ0K0pKbzNLWTB0dmNtVERrR1JMVjZXWURwbXpsbVFMV0MvK0dSRHRiV0RVSHFBeVMxV3JYRk1wR2RNajJsTThkWGhuUnNhNTUxVlB6NUk3NjJyQUdrRjZEaWtMK1Q3TW42R0JCK1BUTUJBUjlFREUvc2VTOVIwTkhlalFTbFo2RTR1bXFGYjFzMndGYUhkRm1tQlQwcUd4WFJ4Q28rSE1PYzZDVWppZ0R2UlZZbjk5S3JVSW96UmgzMFZ6T0Mwc2JhNGRHZGtjNm9sMW5MYVhRVitEUUVRemRTTER1RUhYRUJXb1VsRldtdEhiSEg4a3RqeFRmMklpejdEenFFeVdIc3Y4dS9vNjRWczNMZHpnWENrMG1Td0V3bDVYNXcrbm0xdEdxTWJ4d1JxYjFFRU9JbGdWMk9BNmF1eEE5SFFmU24vbEtQUnoxbWF4c0dwRVJueVJEVVFDemV2ZTZvLzR2T2hxWkJTWlhjT1g0eHR0T25PeU8rR3Z4Vno1WXVnSVlJVVNMYm15RFNySUt1dnRqU0pEOVZpc1Z0RnpZTVNkSlJsNHYzT3pOZThTeERmYnNuOVd4RjBPRXFveTlwaEo0dk9qNkdPcHdONUxna0NoZEh6dXNtT1dkeEdZQlYiLCJtYWMiOiIxODZlOWNlM2UxZmVmMWY4YzViODhlYTViMjYxODcwYzlmOGFmYmMyYjU4MjNiMWQ5ZTViMzc3ZTYzNDRjYTkxIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFycFRIV2x3ZTJ6dzV3a0pBNFMvZXc9PSIsInZhbHVlIjoicHU0U0l2SXEwN1BsSkRiVHVCYmN3OWY3c3NIUnJnaVNjL3FxYlRhR283M2YraElZOXFBQm1tejZ3MXpLeDB1OThuSkFmRFZacWVZaW5YWTErSmZoU1VvRHhoQmF3M2RFQXFwaE16UHdvWExoMnF4aEl4andPMXc4ZndCYmpvUFlUVUlKenlPazE2WVNvd01lb0R2aVJmanBNa1F2R1lnaThTWEN5OEt0Ykl4Y1RqMUlrdXh0ZVBGUEFweXJKK0JBUEJSYi8zQ25Sd1RHL0JlVEdhU2JxMjRNTEZJRTBIQytqdG1qQ0hZQXRUemJ3SEtjZlAxNWhwUHRoMmJOdzd0c21Cc29oM0grb3JCdUozcGFSNHk5UEo1Y1NBMjUrdDU2bThvY0pLRmxEeDRnYXJhVVZveDFwY1hYWkV0OE5jT2RJTVdtWFVGTndNMWdBYThrQ1pKUkw1LzVwSzB4S1VvOTMrS2tqempBNlhlQ2I0OFNXVkVaQWZYb2ZuNElUUVZwY0lWQktuUllyNWZQTEpXRnFubHdCSGdBQTl6dVRHWDlVSGlxanpsRVNteVkwVEp5eWpBdkFobFNTYjE0cnR6L243azZCTkYxSnVmTFhPbC9rVVdhTEUrOEh5SDFYUzNlRWF3ZWtCMUt6S2FJenhOOUF1WGp5elF6V2NWRHl5aE8iLCJtYWMiOiJiN2M0MWIxYjM0YWU0YTY5ZjVmMTU1YmI3YzdjMjA1ZTVmN2JkNTE0MTVlYWFlYTFjOTVjNzQ4ZjNmOWQ2ODNkIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Img1Y2pRd0s2dFFyVkgxTmNoUktHM3c9PSIsInZhbHVlIjoiNDBBVm9xMFBTOWdOYjkyRmZpL0JpdVBRR0EzVDZRVTRoOVQ0K0pKbzNLWTB0dmNtVERrR1JMVjZXWURwbXpsbVFMV0MvK0dSRHRiV0RVSHFBeVMxV3JYRk1wR2RNajJsTThkWGhuUnNhNTUxVlB6NUk3NjJyQUdrRjZEaWtMK1Q3TW42R0JCK1BUTUJBUjlFREUvc2VTOVIwTkhlalFTbFo2RTR1bXFGYjFzMndGYUhkRm1tQlQwcUd4WFJ4Q28rSE1PYzZDVWppZ0R2UlZZbjk5S3JVSW96UmgzMFZ6T0Mwc2JhNGRHZGtjNm9sMW5MYVhRVitEUUVRemRTTER1RUhYRUJXb1VsRldtdEhiSEg4a3RqeFRmMklpejdEenFFeVdIc3Y4dS9vNjRWczNMZHpnWENrMG1Td0V3bDVYNXcrbm0xdEdxTWJ4d1JxYjFFRU9JbGdWMk9BNmF1eEE5SFFmU24vbEtQUnoxbWF4c0dwRVJueVJEVVFDemV2ZTZvLzR2T2hxWkJTWlhjT1g0eHR0T25PeU8rR3Z4Vno1WXVnSVlJVVNMYm15RFNySUt1dnRqU0pEOVZpc1Z0RnpZTVNkSlJsNHYzT3pOZThTeERmYnNuOVd4RjBPRXFveTlwaEo0dk9qNkdPcHdONUxna0NoZEh6dXNtT1dkeEdZQlYiLCJtYWMiOiIxODZlOWNlM2UxZmVmMWY4YzViODhlYTViMjYxODcwYzlmOGFmYmMyYjU4MjNiMWQ5ZTViMzc3ZTYzNDRjYTkxIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFycFRIV2x3ZTJ6dzV3a0pBNFMvZXc9PSIsInZhbHVlIjoicHU0U0l2SXEwN1BsSkRiVHVCYmN3OWY3c3NIUnJnaVNjL3FxYlRhR283M2YraElZOXFBQm1tejZ3MXpLeDB1OThuSkFmRFZacWVZaW5YWTErSmZoU1VvRHhoQmF3M2RFQXFwaE16UHdvWExoMnF4aEl4andPMXc4ZndCYmpvUFlUVUlKenlPazE2WVNvd01lb0R2aVJmanBNa1F2R1lnaThTWEN5OEt0Ykl4Y1RqMUlrdXh0ZVBGUEFweXJKK0JBUEJSYi8zQ25Sd1RHL0JlVEdhU2JxMjRNTEZJRTBIQytqdG1qQ0hZQXRUemJ3SEtjZlAxNWhwUHRoMmJOdzd0c21Cc29oM0grb3JCdUozcGFSNHk5UEo1Y1NBMjUrdDU2bThvY0pLRmxEeDRnYXJhVVZveDFwY1hYWkV0OE5jT2RJTVdtWFVGTndNMWdBYThrQ1pKUkw1LzVwSzB4S1VvOTMrS2tqempBNlhlQ2I0OFNXVkVaQWZYb2ZuNElUUVZwY0lWQktuUllyNWZQTEpXRnFubHdCSGdBQTl6dVRHWDlVSGlxanpsRVNteVkwVEp5eWpBdkFobFNTYjE0cnR6L243azZCTkYxSnVmTFhPbC9rVVdhTEUrOEh5SDFYUzNlRWF3ZWtCMUt6S2FJenhOOUF1WGp5elF6V2NWRHl5aE8iLCJtYWMiOiJiN2M0MWIxYjM0YWU0YTY5ZjVmMTU1YmI3YzdjMjA1ZTVmN2JkNTE0MTVlYWFlYTFjOTVjNzQ4ZjNmOWQ2ODNkIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733686586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1394355320 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1394355320\", {\"maxDepth\":0})</script>\n"}}