{"__meta": {"id": "X7ce47952e501bdfe7827d01e936d6bac", "datetime": "2025-06-13 08:58:26", "utime": **********.628475, "method": "PUT", "uri": "/users/16", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749805105.802393, "end": **********.628508, "duration": 0.8261151313781738, "duration_str": "826ms", "measures": [{"label": "Booting", "start": 1749805105.802393, "relative_start": 0, "end": **********.382925, "relative_end": **********.382925, "duration": 0.5805320739746094, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.382938, "relative_start": 0.5805449485778809, "end": **********.62851, "relative_end": 1.9073486328125e-06, "duration": 0.24557209014892578, "duration_str": "246ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47310880, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT users/{user}", "middleware": "web, verified, auth, XSS, revalidate", "as": "users.update", "controller": "App\\Http\\Controllers\\UserController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=252\" onclick=\"\">app/Http/Controllers/UserController.php:252-315</a>"}, "queries": {"nb_statements": 13, "nb_failed_statements": 0, "accumulated_duration": 0.05158000000000001, "accumulated_duration_str": "51.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4280179, "duration": 0.02025, "duration_str": "20.25ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 39.259}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.468668, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 39.259, "width_percent": 1.822}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.49949, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 41.082, "width_percent": 2.326}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5047581, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 43.408, "width_percent": 2.346}, {"sql": "select * from `users` where `users`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 284}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.514757, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "UserController.php:284", "source": "app/Http/Controllers/UserController.php:284", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=284", "ajax": false, "filename": "UserController.php", "line": "284"}, "connection": "ty", "start_percent": 45.754, "width_percent": 1.377}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>' and `id` <> '16'", "type": "query", "params": [], "bindings": ["<EMAIL>", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 983}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.543933, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "ty", "start_percent": 47.131, "width_percent": 1.997}, {"sql": "select * from `roles` where `id` = '23' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["23", "web"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 120}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 298}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.550975, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": "vendor/spatie/laravel-permission/src/Models/Role.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "ty", "start_percent": 49.128, "width_percent": 1.958}, {"sql": "update `users` set `type` = 'Cashier2', `users`.`updated_at` = '2025-06-13 08:58:26' where `id` = 16", "type": "query", "params": [], "bindings": ["Cashier2", "2025-06-13 08:58:26", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 301}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.557281, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "UserController.php:301", "source": "app/Http/Controllers/UserController.php:301", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=301", "ajax": false, "filename": "UserController.php", "line": "301"}, "connection": "ty", "start_percent": 51.086, "width_percent": 7.716}, {"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 3059}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.566164, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3059", "source": "app/Models/Utility.php:3059", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=3059", "ajax": false, "filename": "Utility.php", "line": "3059"}, "connection": "ty", "start_percent": 58.802, "width_percent": 1.978}, {"sql": "update `employees` set `name` = '<PERSON><PERSON>', `email` = '<EMAIL>', `employees`.`updated_at` = '2025-06-13 08:58:26' where `user_id` = 16", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "<EMAIL>", "2025-06-13 08:58:26", "16"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 3061}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 302}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.575536, "duration": 0.009859999999999999, "duration_str": "9.86ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3061", "source": "app/Models/Utility.php:3061", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=3061", "ajax": false, "filename": "Utility.php", "line": "3061"}, "connection": "ty", "start_percent": 60.779, "width_percent": 19.116}, {"sql": "select * from `model_has_roles` where `model_has_roles`.`model_id` = 16 and `model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5917819, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 79.895, "width_percent": 3.218}, {"sql": "delete from `model_has_roles` where `model_has_roles`.`model_id` = 16 and `model_type` = 'App\\Models\\User' and `model_has_roles`.`role_id` in (17)", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User", "17"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5985398, "duration": 0.00567, "duration_str": "5.67ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 83.114, "width_percent": 10.993}, {"sql": "insert into `model_has_roles` (`model_id`, `model_type`, `role_id`) values (16, 'App\\Models\\User', 23)", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User", "23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\UserController.php", "line": 306}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.609128, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "UserController.php:306", "source": "app/Http/Controllers/UserController.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FUserController.php&line=306", "ajax": false, "filename": "UserController.php", "line": "306"}, "connection": "ty", "start_percent": 94.106, "width_percent": 5.894}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-465094817 data-indent-pad=\"  \"><span class=sf-dump-note>edit user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-465094817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.513825, "xdebug_link": null}]}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "User successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/16", "status_code": "<pre class=sf-dump id=sf-dump-762319553 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-762319553\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-655816346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-655816346\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1285662432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\"><PERSON><PERSON>asi<PERSON></span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"2 characters\">23</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285662432\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-401317738 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">135</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749805096447%7C8%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJIcHo0dG5xNUZSeUtlVTBRUm5aTGc9PSIsInZhbHVlIjoiZE9QU0lJZkhFRHpYZExsOUY3dEJUZjkrQW5ZbGphS25iaWVOeDBucGpPc0JocVhZbEEyQVRHL3dnVU1xczYvTE5xVzVqbkswQVlkUDNHSXA2SHZyaFF6VTk0U1Ztek1hTUtMZVlEQ2NHcE9JTnB5c3dSbmx0c0YxRkY2bkYxaFo2YkVHN3ZCd1lXOU9EWlJYa0szWXBZcGpUOW9GeGwwK2NDZTdjWHJWR01KdkFuNHg2d3l6MWJYRXpmL1RwNFRkY3RXQVF2T0tQeGNLbHF1NjducTAwdGVmWHphc25mU0NIWjJxK240MU9QUWhZbnRvOThEY0hoY3RLb0FhdUFWcm5LcU1BQzlvUUp5ZFVmMEp4eDVaTHlHSnh0TDE2VCtkS3g1TFhxZ0lRWDAxN2hEdERHZFJXM0QvOHlSYUJGOTZNamlMWmhPWHNkRlBVK01mVVUrMW1TNG1wREliK2x4TkM5WEluS1ZRNWJLS3NsT3pGd0lLQUZNU1o5dUtvUDJCL0x6U0NLQ2hmeG15Mll1cUZZbVZEZ3pJeWwwLzRZY3NmbWJCR0pzL3Yzb0tpTzVaUlhtL0FQZ0pSdldGSXRPczBlY3hSOStOVHFCanVrQ0JXYmF0bGZuY05pT3ZmOU9BcE1xdTZuU2xWWXlpZDFLQkdlaFZQSnh4SENCeDFKZjMiLCJtYWMiOiJmNjg4NzY4N2M2ZTEwY2Q2OTE3Mzk0YzNkMzNiMThjNjM1MmU3MjFhMWJhOGM2ZGZhY2ZhYzU1MjE4ZGQ3Y2Y4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im5SN2pPdVovdFowVW1OeHJwem5vYWc9PSIsInZhbHVlIjoiTkRjWk1TUVZQNzdhNDNiUUpYRDZ5ZHlNS1oyaXJLU0c3dHJLYTRESEVvS2NUS0RiTGFDZmNISmJ5eEpLZUZ0VXFPY0p4NXUvTHlwdzg4VVZoYyt5NytMNzUySVpNTGo1cVFHWk1hNzdPMlBjM0FYZnRTT2g4QkZkVTk1STJnSTNjKzhLWnBCaittN09tY1hjbVg0bUF2cEFFVmd5RWdaMHYrNncwU29VL3FCSlpkOFRLUXNLTFhXaTY0ZStBT2taQTB5ZGtJT3VWbTBaWEdhQkVsYXVaaFVEcGtOc3Z1a3J0N1BJR1VOVGIxdzFEQXJheFpXRHdtVW5kTzJFWEY1N05EWG5ZQkpOMnJjVjZQVXhMTGhpbkQ1SURRTnFlL215K1d2SUVoZThpbFVxLy96Sy9qMk9XV3krSU5oR28yNitxQTh0TXVTRHlmL09VSTd4dlhTL1BqMW44Y3l0a1psSHc3Q3dmbXVtenJZY0dVRVlsUjdNeStXSWJ2ZEMrQUZvUHczcG5VdmRxWkRJZmxibGlsSHVydUZubjBqVHVyU2tJM2l3bXBlSTlBZXJaRUsvNXFOWWJtWlMvVzE4NnlpbTJEWGJiOU1HdFFBRXFLWVUwWXc1YWRsMDNSVjQxSHRqaUFuemVsMDBwVjQ4TXJhT05MYUNyMEdvM2xuMlljUmciLCJtYWMiOiI0MWQ0MGMzMTMzNzJmMzY5MWY0ZjZkYjIyY2ViNjNiYThkNWMxMjFjZTYwMjQyYzA0Njg4ZGYxYzA1ZmJhYjUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401317738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-459795356 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459795356\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:58:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVMMUFXZWxYa3JPcGVzT0p4OE1sYWc9PSIsInZhbHVlIjoiNEpWaXFkTFp2UDhDOUswV1cybUdhN2JGSy81aUZRYmpvNlpzZWxNNHUxUXZDZkJDeXZsQTZLYzFQSlF0MG5KUElVQ2FTcVM3OGNZYTdBUGwwanBtTlU4SjNzb0tXZTV1UUE0cmRZblBYUlkzOGRIY3ZLM2FyMjFiZThKUFM3N3M1QlBURmpaMWV2QTRsMVhOYVVuWi9NVkRFcWNjQVlYSDFRZk1ETkJCSFRZUVRyb3ZrNG41ZVMxMWtJNjd3NEVaVlJydTNVeGdNa0V5V3hxb0VzalhYQjNJSG95SGRWR1NaV3doMDNON2lOTEcyMk5rWHFYcGx6QWc4Y1h4VkkwaEhRTU9ZWkwvcklwaUR0MHY3REdsSGFzNFZpNGl1M2p1SjVQOXRyTzc3NzkyRmtVOUx2czhNRGdEd3l0ejBlQjdmQlRkZE5MWWYyN2UrN0FMZmh0MVEzeTJ2TlFzZ0pnQ0haMEhZMnlyM2FWNlZZeThFa1FlcGJxWXphZHZCOVY4R1E1ZEtEQTBBYlNvZ3F4THdVa1hVWk5JdjBLQm5TL2dYTE1RaUxXMzViVUt3Z1hOOC9pN0ZDSWw0UEZQeUpFamt6VE5mSm1VYm5IckZSbkVzMlVwQ05SaTQzSG5qZlZQVTFRbTRuWEpCVTJSaHIvZWkrUU1RWmQ5SGppLzY0NjUiLCJtYWMiOiJhZmI1ZjFhYzEyNTU2NzY0NGJhMzkwZmIzYjlkNDY4MjI5N2E4ZDRhNGRmMjM4MTc0ZjlmOGM3YmMyYmI3MDkzIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJucUFoeGYxM2M0ck4zUVh2ZkZObGc9PSIsInZhbHVlIjoic0dxMWFzTGtKVFV3N0RQMTZYWG1uMFZMMzcySnh4QmZlYkRQWlpHRmViMC9JWVRjUkkzbGtBYnl0bG03akMrazJMOHFFRkhIcDcydk1YRXF1R3pxVUo0THJ5eUpXRUxJSkJnRzExSTJGQzk5bVdLYkt0L1dGWGltMjY4czZYL0pvdFBiSjNTMFg4WVlGNEJlSUlHaUQybUJsc3ZxcHRYUFdaRmlja3llV05rMTc3OHkxTUlzTXhseWxCeHRrV0wzK2VDMnNHVTZEZEhZV0dJWWF5MUZQcXNLT3FiSEhSdjF2SGNIRWxyKzR4a3BwVEJMS0g2dlZLN04xakcweHprY2EyTnlHRWlCVVlUbjNsdVV6bHBMcG9mT3pid05ieE10SS8vSVk4U1ZibFlENjkzR21zUndYMkRrajlZSUlrOEJqTi9YY0FXbHJleWdVOTNLeXFlUzNrOFpxZDd3TTRpTmxZVENha0tEVHhuWXIyaGVoTU84NFZYK2VuSXhYa3hTQk9SeWFoeFNjd0FjTkN1ZkE4UXVRR3MwSldqeG5IcUpQSnhsT0JTYUVSREd3d0Y0YUdvaUxwcUFuWlRVLzZ4Y2ViY1VmbUlTaUlwOHQ4OW9IK2JWL09CWnZwVDJDRDZnNGlEWHV5OVp1YTFIZEloRTg2SVJ4Skc2NGxONzl2TnUiLCJtYWMiOiIyMzg2YmM1OWEwMmM1Mzc2ODJlYjRhOGQzNmFhYmYxMDlhNDVhZjViNzE0OWFjOWM0NmY1M2Y4YWFkNWM3YjFiIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVMMUFXZWxYa3JPcGVzT0p4OE1sYWc9PSIsInZhbHVlIjoiNEpWaXFkTFp2UDhDOUswV1cybUdhN2JGSy81aUZRYmpvNlpzZWxNNHUxUXZDZkJDeXZsQTZLYzFQSlF0MG5KUElVQ2FTcVM3OGNZYTdBUGwwanBtTlU4SjNzb0tXZTV1UUE0cmRZblBYUlkzOGRIY3ZLM2FyMjFiZThKUFM3N3M1QlBURmpaMWV2QTRsMVhOYVVuWi9NVkRFcWNjQVlYSDFRZk1ETkJCSFRZUVRyb3ZrNG41ZVMxMWtJNjd3NEVaVlJydTNVeGdNa0V5V3hxb0VzalhYQjNJSG95SGRWR1NaV3doMDNON2lOTEcyMk5rWHFYcGx6QWc4Y1h4VkkwaEhRTU9ZWkwvcklwaUR0MHY3REdsSGFzNFZpNGl1M2p1SjVQOXRyTzc3NzkyRmtVOUx2czhNRGdEd3l0ejBlQjdmQlRkZE5MWWYyN2UrN0FMZmh0MVEzeTJ2TlFzZ0pnQ0haMEhZMnlyM2FWNlZZeThFa1FlcGJxWXphZHZCOVY4R1E1ZEtEQTBBYlNvZ3F4THdVa1hVWk5JdjBLQm5TL2dYTE1RaUxXMzViVUt3Z1hOOC9pN0ZDSWw0UEZQeUpFamt6VE5mSm1VYm5IckZSbkVzMlVwQ05SaTQzSG5qZlZQVTFRbTRuWEpCVTJSaHIvZWkrUU1RWmQ5SGppLzY0NjUiLCJtYWMiOiJhZmI1ZjFhYzEyNTU2NzY0NGJhMzkwZmIzYjlkNDY4MjI5N2E4ZDRhNGRmMjM4MTc0ZjlmOGM3YmMyYmI3MDkzIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJucUFoeGYxM2M0ck4zUVh2ZkZObGc9PSIsInZhbHVlIjoic0dxMWFzTGtKVFV3N0RQMTZYWG1uMFZMMzcySnh4QmZlYkRQWlpHRmViMC9JWVRjUkkzbGtBYnl0bG03akMrazJMOHFFRkhIcDcydk1YRXF1R3pxVUo0THJ5eUpXRUxJSkJnRzExSTJGQzk5bVdLYkt0L1dGWGltMjY4czZYL0pvdFBiSjNTMFg4WVlGNEJlSUlHaUQybUJsc3ZxcHRYUFdaRmlja3llV05rMTc3OHkxTUlzTXhseWxCeHRrV0wzK2VDMnNHVTZEZEhZV0dJWWF5MUZQcXNLT3FiSEhSdjF2SGNIRWxyKzR4a3BwVEJMS0g2dlZLN04xakcweHprY2EyTnlHRWlCVVlUbjNsdVV6bHBMcG9mT3pid05ieE10SS8vSVk4U1ZibFlENjkzR21zUndYMkRrajlZSUlrOEJqTi9YY0FXbHJleWdVOTNLeXFlUzNrOFpxZDd3TTRpTmxZVENha0tEVHhuWXIyaGVoTU84NFZYK2VuSXhYa3hTQk9SeWFoeFNjd0FjTkN1ZkE4UXVRR3MwSldqeG5IcUpQSnhsT0JTYUVSREd3d0Y0YUdvaUxwcUFuWlRVLzZ4Y2ViY1VmbUlTaUlwOHQ4OW9IK2JWL09CWnZwVDJDRDZnNGlEWHV5OVp1YTFIZEloRTg2SVJ4Skc2NGxONzl2TnUiLCJtYWMiOiIyMzg2YmM1OWEwMmM1Mzc2ODJlYjRhOGQzNmFhYmYxMDlhNDVhZjViNzE0OWFjOWM0NmY1M2Y4YWFkNWM3YjFiIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"26 characters\">User successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}