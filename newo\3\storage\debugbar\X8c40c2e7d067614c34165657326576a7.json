{"__meta": {"id": "X8c40c2e7d067614c34165657326576a7", "datetime": "2025-06-13 08:58:35", "utime": **********.100381, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749805113.687608, "end": **********.100415, "duration": 1.4128069877624512, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1749805113.687608, "relative_start": 0, "end": 1749805114.925865, "relative_end": 1749805114.925865, "duration": 1.2382569313049316, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749805114.925905, "relative_start": 1.2382969856262207, "end": **********.100419, "relative_end": 4.0531158447265625e-06, "duration": 0.1745140552520752, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 42566896, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02549, "accumulated_duration_str": "25.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.040363, "duration": 0.02549, "duration_str": "25.49ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "jbtMkng0tvxhKHxUEliFrsxOT4cipjUmlsYT4kSB", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-326476009 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-326476009\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-509835103 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-509835103\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1882761028 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882761028\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1489652421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1489652421\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-160510156 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:58:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRxVDFOQnVtMml5bno1eERhbDByNVE9PSIsInZhbHVlIjoiMWZ6WVkzcXlUQXVCQTlMQ21jZDVEbDRKM3prVERJM2E0V1NwNmxFVXlLUHJyMHhPY0VDSWx6YmQ3eUxSMG5ybmxENEVyVUJ0NEJaazFLWEYvMGdyRFc2RmUzWFZQbUJkV1hQai9VOVBGQ2Vtelg3M3pCeUZma2ljVU9RSVpSR0lQSHRFQUMzR3B6Q3Q5bkhYbGZnanN6cFRuZ3E5L3c3VmQzREZaK3pJajdsWFRJLzBHV0VVSk0rNVRTRjBNbU1KOVdpTTVwS2dWNDB6ZVgvRzlFMTk4eVc3ZVpDWEtwTHNlOG1BRFlxZGUvMWFEdE55cU05Qjh5aEdUUlEzUkhTL2h2N0xLTFN2dkpWNjNNd3FrQjZ3N3dlT1JIdFRRYUdKeG5VOWRUQjl2WDZ2cUgrWE1qYm1za00zUHc0Y2FKVzN2RCtkZkFxKzBtZUp1aE1obGlJMEloNU5OZzc4cWVvVlpCVzM1MHlVTWljSnArUkZFZlIzWU9GRG5tcUVSQm5xUklvR0VmMXRhSXNDSlhrY1B4VllVYWJiaGduZEdwL2dpT25kaVpiaGNTdDRtR2xzREk0bXNNd09nQ2VOWktyMmo5aDdkUGloL3Z5N2xRQzJQVFp4V1Rrb24wUEE2ME43ZHFiTGlWOG4rdXRTeXBzNGI4Ny9XWVhCZWFYeExQNHUiLCJtYWMiOiIzODljNDcxYjVjZTNlNzI5MzI0Njk4YWRjNDA4MmE4YmZmOGJiOGEzZGI4Nzc2N2ViODQzY2EyYWE4MGIzYTRhIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImFWMnJOTlc3WlY1dEsyczVjMUdWMWc9PSIsInZhbHVlIjoiREJJVTYvTGtWVzBPcHFlRnoyQXVmd2Y0MDNxOFlhNXRLaHMvU0VNa2IyNFlncExGMWl1NG45ODd4MVpaNmdscE5ZL2FhL1NEcWZCMVBac3JBMEZITDJwRW9XSGZYQkNCOTdiTFlSKzdvcUR3N05Lc3Z2dFVUMU5kTUtvOXNNRERNTTNWRDY2cnJWVnhEVEdWSjhrSUkyVUhWV0xQMStyVStNNmhlZGJITlA1M1ZQVjdpd2UwK0xUTk91d3VTUjhwTXZwVmNuRmsvdnNwNnlmYzFUb1ZMV0U4SUJnNlhqVGZNSHUzbHZNNnZQS2dUalFvTHNNbGFQd3BtdDdhalduUUlXZVdUYXlBTForNFBHcTJoUm1iQkZ3T1BDK3ZNcnBaMFlLakFXSUtnYW5JT1dmckUwYmJFK3BhUk1oclEzcWxaaUJyTmIxMmpTT2hLVCt2bjJzdmpkbTl5TEJUWEM0Rk9rbjJxRmt1OHIwcThGL25DYXF5bzdYek1xTnkrMkxSRE5mY2RqMmxUN0J3K2NWSzkvbzRXejdlV2NXMGxSOUF0aDYvN3ArR3VWaU11V2c3L0xTMkRoRG4zdVZQVDNYRWlEaWFGOXdYUldFc01Pb2d4ajJiNVZaKzNmWWI5OCtaNTREV2s4Wi9hQ2xMdklwQnFTdmpkcVRFWXhoeXp4QjAiLCJtYWMiOiI1ODBhMDBmYTc2YjNmMDk0ODU2MDA5M2ZkNGQxZDQyODliMmU1OGMxZWE5MjRiMTQ4YjA5NTMyNDA3YmE4YmRhIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRxVDFOQnVtMml5bno1eERhbDByNVE9PSIsInZhbHVlIjoiMWZ6WVkzcXlUQXVCQTlMQ21jZDVEbDRKM3prVERJM2E0V1NwNmxFVXlLUHJyMHhPY0VDSWx6YmQ3eUxSMG5ybmxENEVyVUJ0NEJaazFLWEYvMGdyRFc2RmUzWFZQbUJkV1hQai9VOVBGQ2Vtelg3M3pCeUZma2ljVU9RSVpSR0lQSHRFQUMzR3B6Q3Q5bkhYbGZnanN6cFRuZ3E5L3c3VmQzREZaK3pJajdsWFRJLzBHV0VVSk0rNVRTRjBNbU1KOVdpTTVwS2dWNDB6ZVgvRzlFMTk4eVc3ZVpDWEtwTHNlOG1BRFlxZGUvMWFEdE55cU05Qjh5aEdUUlEzUkhTL2h2N0xLTFN2dkpWNjNNd3FrQjZ3N3dlT1JIdFRRYUdKeG5VOWRUQjl2WDZ2cUgrWE1qYm1za00zUHc0Y2FKVzN2RCtkZkFxKzBtZUp1aE1obGlJMEloNU5OZzc4cWVvVlpCVzM1MHlVTWljSnArUkZFZlIzWU9GRG5tcUVSQm5xUklvR0VmMXRhSXNDSlhrY1B4VllVYWJiaGduZEdwL2dpT25kaVpiaGNTdDRtR2xzREk0bXNNd09nQ2VOWktyMmo5aDdkUGloL3Z5N2xRQzJQVFp4V1Rrb24wUEE2ME43ZHFiTGlWOG4rdXRTeXBzNGI4Ny9XWVhCZWFYeExQNHUiLCJtYWMiOiIzODljNDcxYjVjZTNlNzI5MzI0Njk4YWRjNDA4MmE4YmZmOGJiOGEzZGI4Nzc2N2ViODQzY2EyYWE4MGIzYTRhIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImFWMnJOTlc3WlY1dEsyczVjMUdWMWc9PSIsInZhbHVlIjoiREJJVTYvTGtWVzBPcHFlRnoyQXVmd2Y0MDNxOFlhNXRLaHMvU0VNa2IyNFlncExGMWl1NG45ODd4MVpaNmdscE5ZL2FhL1NEcWZCMVBac3JBMEZITDJwRW9XSGZYQkNCOTdiTFlSKzdvcUR3N05Lc3Z2dFVUMU5kTUtvOXNNRERNTTNWRDY2cnJWVnhEVEdWSjhrSUkyVUhWV0xQMStyVStNNmhlZGJITlA1M1ZQVjdpd2UwK0xUTk91d3VTUjhwTXZwVmNuRmsvdnNwNnlmYzFUb1ZMV0U4SUJnNlhqVGZNSHUzbHZNNnZQS2dUalFvTHNNbGFQd3BtdDdhalduUUlXZVdUYXlBTForNFBHcTJoUm1iQkZ3T1BDK3ZNcnBaMFlLakFXSUtnYW5JT1dmckUwYmJFK3BhUk1oclEzcWxaaUJyTmIxMmpTT2hLVCt2bjJzdmpkbTl5TEJUWEM0Rk9rbjJxRmt1OHIwcThGL25DYXF5bzdYek1xTnkrMkxSRE5mY2RqMmxUN0J3K2NWSzkvbzRXejdlV2NXMGxSOUF0aDYvN3ArR3VWaU11V2c3L0xTMkRoRG4zdVZQVDNYRWlEaWFGOXdYUldFc01Pb2d4ajJiNVZaKzNmWWI5OCtaNTREV2s4Wi9hQ2xMdklwQnFTdmpkcVRFWXhoeXp4QjAiLCJtYWMiOiI1ODBhMDBmYTc2YjNmMDk0ODU2MDA5M2ZkNGQxZDQyODliMmU1OGMxZWE5MjRiMTQ4YjA5NTMyNDA3YmE4YmRhIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-160510156\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-922076602 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jbtMkng0tvxhKHxUEliFrsxOT4cipjUmlsYT4kSB</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-922076602\", {\"maxDepth\":0})</script>\n"}}