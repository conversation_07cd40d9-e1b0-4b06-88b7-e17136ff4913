{"__meta": {"id": "X93b66e15b8a5f7796ca1bc8ebf6259a9", "datetime": "2025-06-13 08:50:33", "utime": **********.565354, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.628204, "end": **********.565377, "duration": 0.****************, "duration_str": "937ms", "measures": [{"label": "Booting", "start": **********.628204, "relative_start": 0, "end": **********.404261, "relative_end": **********.404261, "duration": 0.****************, "duration_str": "776ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.404282, "relative_start": 0.****************, "end": **********.565379, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02572, "accumulated_duration_str": "25.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.469534, "duration": 0.02305, "duration_str": "23.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.619}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.507382, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.619, "width_percent": 4.082}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.544651, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.701, "width_percent": 6.299}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2953794 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2953794\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804631255%7C2%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBlL2hRaHJURENvNHdMK2xONjYxMnc9PSIsInZhbHVlIjoiNlJlZ2RQYWswZlM1MFFRb3hWZ29pNXNaK0xSenZVR08zVGtnMUlFbWNTTE9uWVRSNzFrdHN4eWRhTlltYWNETjhxcEtIQ2o1c0d3d0tWZlFLOHlWZmJJRDdHcVk4V3cvMW9NdkpJbG1BY3l4UDFtdnVGa3RwWnZsMzZaMEFWWHBRcWRoR2NPQTV1cUxybGFJQU9QVFFkYncvOVNqQlRZWVlMclA2OEpFQWdERmpVS2dzRmh0OER3TVZJSURNanFTUVJaS0tYK1lCdkRBZjhCcGpsWEc1eW1PcC9oZ3BsWVNIS2lOVkVZQ1VuMUF6WExQeFFzcHZPZzNXbzM3Nno5K3RWaHVGZWtkeFdrZE5VbHhORktRSDVvMXNtZEs3NmZLazNCWE93SWVORnJScWtJTWl0VG1NbE56eXQ5K21rZmozOERIbFF6Vkc1WEU1bG8zckgwVGJodmV3cWVQZGlvVU1IdGVzNDFKQ08rTVFoK09KNFNIeEEzVURnMm9DSDR3cGsybHEyc0VzNElNN2pIV0tUQTRRWm43RC9ab1U3MXR2MzdUKzB3NW55cTA1RnRCMnY5TUMzWjI5a25qN2p3MnF3QXh6bWV0cHUwNzY2a0RzKzBVSTZWNm0xNFptdC8wQmpZUllmZ1RvVE1TWmtNSnlrMGtvMStzYjh4ME5vR1AiLCJtYWMiOiIzYWRkYWZmNTFmYTRjOTFjYWYzY2Y2ZWM0YTI4ZjMyMWU5NTg3ZTA4NWY3MDkxYjA5Zjk4ZGYyNTBhZmYwMzhkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InlCSE5HV3l2b1hvclBrUUh1UlVURFE9PSIsInZhbHVlIjoiRmt0Z0tzbUVxcGhtR0QzZWVjUkx0VUVHaVdhU1N6Q2JQRjVrTDhCZlMyanJ6UTlHUHBwWDJnRWppY2FKTUQ3cGwydHZ6VTRTOFlQTTNGdkZsQTFDVEQvZ0NOaGpaWkN6MXpINTFDdnNsTHVqNEEvNkZJT2VLajl0QVJ4ajdzbGFaMXJxYTNzcGpibEpiVk12RE4yOU9USGxnRGRZU2ZBUmZheXV1b1BkdXV2bDZ0enVJZ1podlFrK1lUNlZ0VUJTOTRHU1haeGZmWjlNVHltcFk0UDhNR1lvR3FpYmVVekQ3UUl1RHpkNHpGZWNnOVBmMWVqRDR4RVhwT2s2d21LRTVtVEFsL2VuTHlSNkNkTTY3c0lnNEtPL2xsZzFwa29sV1B5UEtMdGtGdXNpYzlGVVgzczlvTXhla1hBUkFhTkd0b2Vkb2xhNnlsdUxWbE9kNUQ1cGFkN0lVSHVoT0g0eUJYU1ZCWmVJOWNjd2RHNC91YmlwVVVsbjFQaVY5c1dzZDU5Y1ZUVmQ0ZXJaMDdnMERncWJYTkdxcUVCNS94a3BVdHJRUTUxMjVpQTZURXFDYTJscEd5cFZwVW1seTNVbjFEbDNVNTlqM0dUaGxsQ0NVbzNXbHlOMUJNR2wrYTl4UzRESjdDdHUvNzlVc3NERkVxT3J5WjBqaStjWHYzdmkiLCJtYWMiOiIwN2QzNzU4OGMwNDQ2MTM2YTRmMWI0MTU4MzNkZDBjYmJiNDVjNWVjMTYxMGUyZDZkY2NmMmE3ZjY0OGVkNDM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-544324542 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544324542\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1870937451 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:50:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktDeWJzemk0WVE5OTVwN1l2WXhFb3c9PSIsInZhbHVlIjoiY3cvZDI4R1o5UW84WmZyRDZXd053UEk3bHlmMksrdFZuWFVScVUwN20ydjJMT3BXZ2dsMStLL282dDlWUnBrR3RSNURTWmFSMWJqOTg3b29OL2NHL3cvQ3NoeEt5ZmZXT05ndHV3S3kxREJYanNuR21vM0xwc0ZJS3JTZ2xZL2U5SG5CaTRGbitRTXJYemVjNWMrb3AzdmZBOCtIUzBOVWxseE9rTm51UTAzcm9abnN0QkwrcnBxZkV1cS9IUDY2cGJkVWdBT2srSnJZZGpYTWdMR3BwVjJRSVh2VFNuMlFzN1lUTzQyTVp1ZzV4TnhJanNBQUZJbzhWUndpa0VyUlRoVXJxYVlleU1LQTFrNEZ5MHpkM0hVb0pobVc3elJaaTBaYS94NkJzUmFVdjUrOWRwNGxmamdOZ2dacEMvQ3p0Ym9ENlVWOG53V1Q1WjRyRVc5MEZ5dzg5R2RjbDNoN00rbUk3ZEtIQ2RvV2owS01RbmtCSDRVVDF5eGgxNERlVDEyWTVOcUVTcFBsendiVEFvR0NDNGZVaVJVTGhJc2JpRTBvLzc4aUNnNEo2U29tdFdGMjVHekh3bnFlUVNEM01QV3RIeG50UVBKUytqRlVQa0NOMVdxNkJlQ28wVmMwaXlYaE5KYUpYOVFvUEN6eGNjZE1IV3p4Wmx1ZUNaUEgiLCJtYWMiOiJjMzNiYTVjNmY2MGRmM2Q4N2EyYjQzNWJiNWQ5MTRjZDIzZjI4NTMxOTFhMzYzNDM4NzA2NzI4Y2U4NWFiMjBmIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ijg4bzMzK1hqV1pxRVduMlBWSll1bHc9PSIsInZhbHVlIjoiVlJjYy9nZWx5am9GTXFialM1dlUwVHMvZThmbzVkM0l2WVU4NlUxSERoaEJoU0Vhd0xBMFdkeUdSRzVJSDBKSXJoK3IyMHhjRzZBU0dhOVlIVjlCL0ttaEJxU3kzOVBYNHNDZ0Exa0FIc3NKSGpvckV6K0dFalJzWUs0YlN3Rit5UW5rdHJBQ2FoM3FFcmpvLy9iWmIwY2RVaGRxNXVNL1FvWEVBbTNLNGhmU3BGMWhXdXE5bjdYVFFGNTNFRXF5Z3lyOEFIamNOM3k2VDhwMGZZd3ZrOGxrenRRalJPQ0pVME03SzI0RS9FMGhETkd3cHJPWFB5Z2ROT2docWpSRmdSZEp0Y0cvNmJOYU8zV2p6RFk1bzd2dVB5OElxeU5JSmpwQ1VjMVN2QTJpenNkTEMxVTkzNVZ3UlEzWHhMdHcrb0kvREhDUHZxQjNrYlNNMDBzN2pKdjdtd2QxYzh5alBIcVhhVmJncXR1ZkRvRTBrUUNYYXkzc1hINFhoVXVocnBweVpLakk0TDVLKzBEQldqOGRiQXc1VVI4c0MzRVdOTFdXZjd5bVlxNGUyQXB0TWVyMmNIR21jclY4U0pvTHhQRFRXODdaVllqVFA5OHNwN0NtYkFHQmRIOXV5TnVrNUd0cFp0VjgwdFUrNUdwU0szSW5hQ2pmeVVqWDFXc0giLCJtYWMiOiJhOGQyNmVlYTFiMDFhOWExYmUyZWQzZTZiMmY4YTgwNDEwOGM1NmRlYTc5ZmYzNmI2MTE3MjY0ZDBmMjI1NDNhIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktDeWJzemk0WVE5OTVwN1l2WXhFb3c9PSIsInZhbHVlIjoiY3cvZDI4R1o5UW84WmZyRDZXd053UEk3bHlmMksrdFZuWFVScVUwN20ydjJMT3BXZ2dsMStLL282dDlWUnBrR3RSNURTWmFSMWJqOTg3b29OL2NHL3cvQ3NoeEt5ZmZXT05ndHV3S3kxREJYanNuR21vM0xwc0ZJS3JTZ2xZL2U5SG5CaTRGbitRTXJYemVjNWMrb3AzdmZBOCtIUzBOVWxseE9rTm51UTAzcm9abnN0QkwrcnBxZkV1cS9IUDY2cGJkVWdBT2srSnJZZGpYTWdMR3BwVjJRSVh2VFNuMlFzN1lUTzQyTVp1ZzV4TnhJanNBQUZJbzhWUndpa0VyUlRoVXJxYVlleU1LQTFrNEZ5MHpkM0hVb0pobVc3elJaaTBaYS94NkJzUmFVdjUrOWRwNGxmamdOZ2dacEMvQ3p0Ym9ENlVWOG53V1Q1WjRyRVc5MEZ5dzg5R2RjbDNoN00rbUk3ZEtIQ2RvV2owS01RbmtCSDRVVDF5eGgxNERlVDEyWTVOcUVTcFBsendiVEFvR0NDNGZVaVJVTGhJc2JpRTBvLzc4aUNnNEo2U29tdFdGMjVHekh3bnFlUVNEM01QV3RIeG50UVBKUytqRlVQa0NOMVdxNkJlQ28wVmMwaXlYaE5KYUpYOVFvUEN6eGNjZE1IV3p4Wmx1ZUNaUEgiLCJtYWMiOiJjMzNiYTVjNmY2MGRmM2Q4N2EyYjQzNWJiNWQ5MTRjZDIzZjI4NTMxOTFhMzYzNDM4NzA2NzI4Y2U4NWFiMjBmIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ijg4bzMzK1hqV1pxRVduMlBWSll1bHc9PSIsInZhbHVlIjoiVlJjYy9nZWx5am9GTXFialM1dlUwVHMvZThmbzVkM0l2WVU4NlUxSERoaEJoU0Vhd0xBMFdkeUdSRzVJSDBKSXJoK3IyMHhjRzZBU0dhOVlIVjlCL0ttaEJxU3kzOVBYNHNDZ0Exa0FIc3NKSGpvckV6K0dFalJzWUs0YlN3Rit5UW5rdHJBQ2FoM3FFcmpvLy9iWmIwY2RVaGRxNXVNL1FvWEVBbTNLNGhmU3BGMWhXdXE5bjdYVFFGNTNFRXF5Z3lyOEFIamNOM3k2VDhwMGZZd3ZrOGxrenRRalJPQ0pVME03SzI0RS9FMGhETkd3cHJPWFB5Z2ROT2docWpSRmdSZEp0Y0cvNmJOYU8zV2p6RFk1bzd2dVB5OElxeU5JSmpwQ1VjMVN2QTJpenNkTEMxVTkzNVZ3UlEzWHhMdHcrb0kvREhDUHZxQjNrYlNNMDBzN2pKdjdtd2QxYzh5alBIcVhhVmJncXR1ZkRvRTBrUUNYYXkzc1hINFhoVXVocnBweVpLakk0TDVLKzBEQldqOGRiQXc1VVI4c0MzRVdOTFdXZjd5bVlxNGUyQXB0TWVyMmNIR21jclY4U0pvTHhQRFRXODdaVllqVFA5OHNwN0NtYkFHQmRIOXV5TnVrNUd0cFp0VjgwdFUrNUdwU0szSW5hQ2pmeVVqWDFXc0giLCJtYWMiOiJhOGQyNmVlYTFiMDFhOWExYmUyZWQzZTZiMmY4YTgwNDEwOGM1NmRlYTc5ZmYzNmI2MTE3MjY0ZDBmMjI1NDNhIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1870937451\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1082808291 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082808291\", {\"maxDepth\":0})</script>\n"}}