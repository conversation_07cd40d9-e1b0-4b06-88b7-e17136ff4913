{"__meta": {"id": "Xbcf73789e89670130ffc111ff174a8e5", "datetime": "2025-06-13 08:51:17", "utime": 1749804677.031096, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804675.653813, "end": 1749804677.031133, "duration": 1.3773200511932373, "duration_str": "1.38s", "measures": [{"label": "Booting", "start": 1749804675.653813, "relative_start": 0, "end": **********.835026, "relative_end": **********.835026, "duration": 1.181213140487671, "duration_str": "1.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.835044, "relative_start": 1.1812310218811035, "end": 1749804677.031137, "relative_end": 4.0531158447265625e-06, "duration": 0.19609308242797852, "duration_str": "196ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44197616, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018770000000000002, "accumulated_duration_str": "18.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.935784, "duration": 0.016120000000000002, "duration_str": "16.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.882}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9826858, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.882, "width_percent": 5.115}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1749804677.003333, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 90.996, "width_percent": 9.004}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1944544744 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804669558%7C3%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilc4K3Bxa1ZpOFpMZ05OeXRKQnQrQnc9PSIsInZhbHVlIjoidnVwL2F4eTNPcGdFYmZodFYvNUQ5R3RrZEp3MXo1bElWY2h4Z0tRZ0ozd3NkcXFQY1BmeHA1S09XN1ZaZU4rL3FPYVF6VHYwUDR5bDdLZllVcnp2dFlqL1d4ZDV2bkpBWXh6ejVadHY0YXBvV05sSURkQk01NDY3cE1kcDdBQ29WNkxQVTJqMklrSU9YREtWdWNpU1lrUEVNbGRPU2ZhV1lacEoxejQ2bDlCQWJtdm52YndqTXZYOG1jUHAzbHB1Vmg0TDh6d0FlNGo3Zi84cjBOMUlmd2trbGdsRGFVRVF4MHMvckxvQlRNT0p1eFZpTzZzNGdmTVp6ZHZXM3BEOC96QWtOaFMycUdhb2xDTWFtWkN2YnYwTTJMRXdsWk1acUVxanNldzNKNTFwU3NDdU01MlNwZDBpOEFrM2FuZkFvZEtLbU9OdFM2ajNrYzVMK1FaUEpmWlpTcEUvMlR2ZUJGajByNHQ0cjdtK0VKN1NHd3lDRXMyelNwVHNyRkxTSlZkTTh0blBzeEZ1ZTVWQnBiNWxXWDFsRmdZTUNYL1ZUK1ZIVVRVY3g2aGdaSDcyMkJUVzZZdkdGMzkrRWdoMTE0YUwwc1crZiswVmJWbTJCelUxR2pzNU1Bb0dUcUh6aGJ5Q0hTcnltVFQvOUJXL3hIcEJPSGJFTHdxVlhxMkMiLCJtYWMiOiI3ODU4ZmY4ZWFkNGM5M2FkNmZkMTU4NTdhOGMzNzc5NzY4YjBlMzA5MGQyZGI2OWY3YjhhMDRkMzk3YTc1ZGU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Indjb2dKZ1NwcW83QjhMSnQ4M0hka3c9PSIsInZhbHVlIjoiTEZkek81SHM5T3dwbUVIdFJwMlNTazdCWS8zZk1VWlN4T0VTdUtkQXdTMXBiVWhSZ1JsUXpUaTJQQnYrQ1lnU3dGWEgvSS85aVlsVFZqV2kxaklzV3ZzdHlOYm1yTTExcnFyamZRTXc3YUdjNnZmTUFhSmkxVm9jL3hha3ZvYzRsVGtjdC83SDRmdm42WTh5WXV6cWJzVklBNVkrY08xQm0xSWI5Vi9LMC9uSzF2Z0dUMjJiYlJrbFNkdU1kenJRYTcwTE0vTTBwRDdOQ3RMcGk5MzFzcWM1RUg2TXh6WHZyTm0wT21RbVhPR1cwclU3ZW81V1NjQmJpdkJsSWg5Um1mbWJPckt6aGRabEowNHg1eVk0cU9VSGVuRjhpNGUwVVltMDRsaExVZDIzQURlemd4NWM5RFFPQXhoU1F2WGJpbXNoSTcvbWIrRjk3emtrZTA5NzNhcnA0UUZ1TW45bWlsN1NQRUNQSmZzRGY0Vlk5T2QyZEpZTm9ndy8rYWdYNFBxWUhFNFQwa05iTGRzM3lqYmZ0azBId2dueG5aZnR4RFVwenhka3hrYnV6eTlNR0RmME8wUmdoK3JwUEVjWkFXRGVVaFJHc2dLZFJGM0ZjaWpPQTBleUs4MnBOQ3cwazFaQmZ4bWk4ekN3anB6eHJrRVFYbFpUb2d0MGoyVlIiLCJtYWMiOiJkM2RmMmYzMDUwNGMxMzBhNTFkOGRmZWNjNjA2ODVhZmQ1MmZkNWQ0MDRlYjM3YWYzN2NjODdjYjNhN2RmMjc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944544744\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-673928679 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673928679\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1333295682 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:51:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9sYjdQRzVzdVZYSVcrWDNvTlNHY3c9PSIsInZhbHVlIjoiR2c4eVV2b0RJSDhFeGhEbFdpM1JsQUh0dlJ2MXRoQ2pPNHlTQTRiY2REQkZDeTFtZmNvZkppTzNnekl6aFNvdVdIdHpyZDRvTWxEUW9sZDNxMldHNktQbVB3dXd5RU8xemFaQ3djYlR2ZEtOVjlnR2gyU2FqTGJDakJPZG5Sd3RMejNzWHMxSSsxOUd6TmV3ZVI3QlRxVWdrbENwRHd1N2gwejByWjVPYVMxUnh6b2swNmswMHFocXRGckJuSXJZcmpjRlJIWUJPY2JMSnRJbjNLanYzTHZDWGR3dTQweE16bm5na0JEbjhFdythTmVBaFJUeExObEZvSTRjdysyNnRvTEJIRlpUZXljWHFIQnkyUDduaFZVcm9sbTlhWUhTVGJ6UHhuMllDMkFmL2tGQkNGUGpoMTVCV2NDYU1vbnp2aEFHeHBkUFIxTzBjbndvajRPLzlsVW9DdGtINld6L25PV2c0ajRyUi82RmhXdTRyR21va1lLM0lTdy9xSjBvc09qUjhrUkJUREZNZXVwVUJDazBsY0k4Tnpnell1NTBCV3gwc3BCUEE2SEkxUUtFUUFJNkFYZHV4NVhaNzFiWWFOeTh6UkN6MjltN2FMZWgrU2Z6VVRoMGt2UkQvZWVYRVRCRUhmUklwUFhLVGZVZWw5QlFUcXczVUp6cEErK2QiLCJtYWMiOiIxYmM5Mjk5NDg1Yzc0YTI5MTU3MzM0ZGVkYjhlODdkNjQ1NGY5Zjc4NmFhYTE1ZWIzYTU2YjhkZjU4NWQxZjQxIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InJMWUs5QW01WUNsd1doekgrMm9RT1E9PSIsInZhbHVlIjoiVGVLQ1VlcC9rOTdvWmJwNktPM1FJVDRVVzd0UkRPZHFuQ0hleTlQSmJqR1NxdWVkT2JLQ01ncVVLcWxIdHlzTnh3YmZSY2NuYzJZQUhQZDA3TXU5NURPdFYvU21WVEJVTzdLVTNFS3FZMWNPY1E5a2FKbEpyelFzZmtDOGRaQTdYbjN3WWdTTHJ6ZmhSWDdpVGlSeFI2M2Q1c2hGRmNlUGN6YitTanA2Q0VDM3ZTVmhKOExQNnArbHd5VVZodzR4YUZLQU91c1l4YWF1RlJ1RGNoN1Fwd0FQKzdNWTBkcFpBbUxlS3c1bXU0cDVvMUpwUDRMUWtBSUUrc1FDVGtFQXBnK3BrbzlLNEJsOVRPRUM3MFRPYmpuNXQ4WEhRMjhLM0VhQ1hiMWhkMmlnemxUTCtPYXJFVlFrMzQrb1FLckM3UkNISkZtSXVNakFLUkFzMGNyc1VwRS9mMFNvbzNqS0FmWndBaEVVbEgybmdFNDBLRDd4VVR4NGlaS0JxdWNjMzFNRkJZWWgwdHZJT0lMR2U5Qm1qbVB3aDNEbWZmVzdMcUxUZ3h1UDhEcnpxOUlMOFRiTklScmprcmpWNEY5THp6WkRZZ0JZdTVrdU12Sy8xOXE3U01CS1JjLy8yV2d1Vjh3clI5d0pLazhHVElhNDZTVmpFdFlVbnFrZXdvVHEiLCJtYWMiOiJlMzZkYjA1ZWRlZTQ0MjkzZGJjYzJkOTk2ZTNlNzY0YTE1NzliNDVjOWU0MTU3NDFlMmMwMjQxODE4ZWY0NzgxIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9sYjdQRzVzdVZYSVcrWDNvTlNHY3c9PSIsInZhbHVlIjoiR2c4eVV2b0RJSDhFeGhEbFdpM1JsQUh0dlJ2MXRoQ2pPNHlTQTRiY2REQkZDeTFtZmNvZkppTzNnekl6aFNvdVdIdHpyZDRvTWxEUW9sZDNxMldHNktQbVB3dXd5RU8xemFaQ3djYlR2ZEtOVjlnR2gyU2FqTGJDakJPZG5Sd3RMejNzWHMxSSsxOUd6TmV3ZVI3QlRxVWdrbENwRHd1N2gwejByWjVPYVMxUnh6b2swNmswMHFocXRGckJuSXJZcmpjRlJIWUJPY2JMSnRJbjNLanYzTHZDWGR3dTQweE16bm5na0JEbjhFdythTmVBaFJUeExObEZvSTRjdysyNnRvTEJIRlpUZXljWHFIQnkyUDduaFZVcm9sbTlhWUhTVGJ6UHhuMllDMkFmL2tGQkNGUGpoMTVCV2NDYU1vbnp2aEFHeHBkUFIxTzBjbndvajRPLzlsVW9DdGtINld6L25PV2c0ajRyUi82RmhXdTRyR21va1lLM0lTdy9xSjBvc09qUjhrUkJUREZNZXVwVUJDazBsY0k4Tnpnell1NTBCV3gwc3BCUEE2SEkxUUtFUUFJNkFYZHV4NVhaNzFiWWFOeTh6UkN6MjltN2FMZWgrU2Z6VVRoMGt2UkQvZWVYRVRCRUhmUklwUFhLVGZVZWw5QlFUcXczVUp6cEErK2QiLCJtYWMiOiIxYmM5Mjk5NDg1Yzc0YTI5MTU3MzM0ZGVkYjhlODdkNjQ1NGY5Zjc4NmFhYTE1ZWIzYTU2YjhkZjU4NWQxZjQxIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InJMWUs5QW01WUNsd1doekgrMm9RT1E9PSIsInZhbHVlIjoiVGVLQ1VlcC9rOTdvWmJwNktPM1FJVDRVVzd0UkRPZHFuQ0hleTlQSmJqR1NxdWVkT2JLQ01ncVVLcWxIdHlzTnh3YmZSY2NuYzJZQUhQZDA3TXU5NURPdFYvU21WVEJVTzdLVTNFS3FZMWNPY1E5a2FKbEpyelFzZmtDOGRaQTdYbjN3WWdTTHJ6ZmhSWDdpVGlSeFI2M2Q1c2hGRmNlUGN6YitTanA2Q0VDM3ZTVmhKOExQNnArbHd5VVZodzR4YUZLQU91c1l4YWF1RlJ1RGNoN1Fwd0FQKzdNWTBkcFpBbUxlS3c1bXU0cDVvMUpwUDRMUWtBSUUrc1FDVGtFQXBnK3BrbzlLNEJsOVRPRUM3MFRPYmpuNXQ4WEhRMjhLM0VhQ1hiMWhkMmlnemxUTCtPYXJFVlFrMzQrb1FLckM3UkNISkZtSXVNakFLUkFzMGNyc1VwRS9mMFNvbzNqS0FmWndBaEVVbEgybmdFNDBLRDd4VVR4NGlaS0JxdWNjMzFNRkJZWWgwdHZJT0lMR2U5Qm1qbVB3aDNEbWZmVzdMcUxUZ3h1UDhEcnpxOUlMOFRiTklScmprcmpWNEY5THp6WkRZZ0JZdTVrdU12Sy8xOXE3U01CS1JjLy8yV2d1Vjh3clI5d0pLazhHVElhNDZTVmpFdFlVbnFrZXdvVHEiLCJtYWMiOiJlMzZkYjA1ZWRlZTQ0MjkzZGJjYzJkOTk2ZTNlNzY0YTE1NzliNDVjOWU0MTU3NDFlMmMwMjQxODE4ZWY0NzgxIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1333295682\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}