{"__meta": {"id": "Xc239d3df8828ec45d2146a7e3c69a513", "datetime": "2025-06-13 08:51:11", "utime": **********.371457, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.420946, "end": **********.371487, "duration": 0.****************, "duration_str": "951ms", "measures": [{"label": "Booting", "start": **********.420946, "relative_start": 0, "end": **********.194131, "relative_end": **********.194131, "duration": 0.****************, "duration_str": "773ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.194153, "relative_start": 0.****************, "end": **********.371491, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02792, "accumulated_duration_str": "27.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.25442, "duration": 0.02444, "duration_str": "24.44ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.536}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.298629, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.536, "width_percent": 5.695}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.34873, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 93.231, "width_percent": 6.769}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804669558%7C3%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxOYWVGeGhrWVE2RXM1Q1ZXZTJPdWc9PSIsInZhbHVlIjoiN3BPeTRxYzlHOVNVS2IyLzlrTndwSnVRdWhFclF1Y3hKbDBwZlVwSG1XWVMvdXFyY290ak54M2ZUOU03eEdHS3Y4NmF4NkFBamFwNVFOTitzS1JGWWV3VFF0TnNFc1A3a2lUaHdDQTlNSkxhbFV3UFFmR2d1SXRKNnZyeGVSbGQ5QnBUQU9WTlZrSFMvSjhicU9BdmNXYzVmazliYzcrSEc1dEo5SGVkb1YvUytzSE1ZSnFnMm02MWRWelRWSzBndnpQZVZSWDQ1QVN1QzJOUWNNb0E0c1ZtMTFLVnBkRFpxbWpRUHdpaUdPeHJLdWpsQnVJeUVCMUdocGI0OUd5c294c1VwRmVFNFNreVhLZlVYeVN5T29wWENUdnliTVd0WDNsUHp4b3V0T1d6WmNKTEV4NWV0VDQ0UHdUVDdyaUpIM0dET2NQRzZCY2dEckpDVWErV0w3eGt3R0dOVWxOOGYvV3JBTGVQSHZzM09oNlZoZnVndTdPeGtUWWtFSVJuYkFxMWdFNDdFSzYycUVjZWZla1NmckVZNmpBcXRmaW5qRG5KcFJuM3FLcGNJVWxTTkgwNFBiSmVKVVhsc2RZeURuSFRIbkM1WW9iMy9ZVHY2T0NjWmZyelpWRDVRcE03MytWTFpoL0c3ZlgrQ3U0dTh6LzUyZjcrYkc2eDFVMTAiLCJtYWMiOiJjZGE1YTM1YjAwNGFhYmViZTA0NTdhNTNjZmM3NDcxMzc5OTk0NTA1YTNhNDA0NmZhNjMxODM0OTljNWNhYWMyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFkaEFtQTJlSStFV2JsVlR1eFhGZ2c9PSIsInZhbHVlIjoiSmdCS0hmeTBwMGJqOEs3NS9zd2JWNlM4ejc5ZmQ3aUMzemMzN3hCb1dUY2F2T0FqRnFySGc3QzBsL3JRa0puVzVkUSsySTMxUUpyQkQzS3J5YW9vd1lPOWpyQmllbHJrSjNPeUZ2MmlzVENMQWZLaEswUWVHZTVxSVl6RnJYOU9ucmkxa05NaEU1dmpveUR4RHJsUytWTjZFQ05scmk5aTZqc2d1a25pWkU1T3FKZW1iVUhBbTBQeEthV0J6UytyWm9hMGF5N0Q2WjFNMWhBZE9wNVB4TTkxUW1xUXVoY1k1azFyclNGc3c0UlU3UTNQd2VlRTZQT3pnVVVXRlRXQXJiMG4wR1YzLzBpa0RNcHRlellJWUp2MXk4M1hINUY4ejVBd0l2K0J6VHlveGZHdFRQKy95dFlHa1VxVmtGWlFrbXV2VUlYb3JNVWJqUmpicFBpa3dFekVLV1VIcTdkelFVSXkrZzRkeTVTWEZnRExzM3VjQVpsYlpvN0dJVkNrSDJCRXF5Sm84TGw2bkhDTnlySEJTZm5GUi9HRVU5S3huWStoOHpqWUdMQlBHR0F6ZTMvOXcrOFJkOS9JKy9IRVN6SWVCUUgyVDhpUTlQVVZBUkRBblNDWTBoM0drVFJYUW5SdjE2TWZKY2pid1pUT25Jcmo1OEVFSDNqdlJhM3kiLCJtYWMiOiJlODE2M2U4YWI4MmZjYjliNWY5ODg5YTkxMGQ3MzE1MjM0YTY1MTRlNGRiOWFkOTU3NDA3NDFmODYxNDk5OGI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-821876394 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821876394\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:51:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ4VGJxK2k0YXlqWk1CVlpDWWp0dHc9PSIsInZhbHVlIjoiaEtxT3pLNVl0SXlEbG1PQWVubDNHaFlDTEI1Wkp4TGhuNW8ydWFNc1ZGcm1nNnBlK3c2UER5MzZOLzdpQjVVQmdWc1g1R1MraEhKZnBCQ1ZWczNVTUI2N3B1ajlBaHFsdkNOamhTc2pUTStJRnJhRm9TeDlNekh6WUZ4NklQUk1mMkt3Vm5GZE5IOUhDZFREQk5xMUhGTklCc1VZT3Iyblc2bTRMaG5Vd3NkcUpIbGNoRWFIazBPdGMwZmhEY21DY1l0aTY5QUhua3Vyc3VVVlJUMDh3VU44T0RyVGs4MnMyaFhVek5hZXVHOVR6M3I5b1VwVUxkN0F3MXI2ZkFGVlZpRGxENDRFMGJVejJZUUVEekZXRzRlWjR3SklZVUY5bk41M1hCS0ZxWC9zQmVpa2RoVkZ4Y2x2S0VmYXAzWDUzN0labGxrbXpRWVJ6cXFBVFM3WXNta2lTY2NOK2hhcEpnWWNUUjdHcEVEL1Jkb01vTktHOGk5b2JGV2ZrekdXemtwc01lUUY0NXUzMzhmNEVsZk5hb0h5cHk1b1VPZ0VLSVlYTytiU1o4QzJ3M0tyY0VrT1lMRlVtUXhiMHEyMnROdno3R3J2NllIc05vR2V2SE8zOEtDWVlFaFk5TUJwTWlQTDZQV0JLY0NrZDlQOHQvNXhDbVl2a3dQVUdqNXYiLCJtYWMiOiIzOTU3NzFmNzdlMmEzYWJlOWZiYWI2YTIxNjdjN2FiYmQxN2I1YWNmOGUwZTRlMWYyYjEwNTQ1OTQxZDY1YTI4IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtrRmt6czkzOFZCUkhhM0x6U290SUE9PSIsInZhbHVlIjoieXNGTDVBcHpxVTAvcU10RkNVK0tKMGdoZEhjVU0wTTl5VEl6WHp6YU5PMUNRSXVhdm1oVjNXd3JaNDVMU2NnNzZyRVM0bnhiVFBnTzMxekVCZ1JnMzRDeGpOMUUxTmRZZml4MHNQYmdQa2ZGcERTRXJqQlpOWHZrNXhwTkdSVFV2bmdPaWpkS2E5cnNnN3lxdy9ybXMwYTFxUUNKL2hXSk1DdmZwTWkwM0NHS0NmNkJpZE4rZXZnZVRkZ3p6SEtvUWk0c092aFUyeVVpWVVrTlZ0azBQTWNNcC85cG9KeVA2QTQ1WGVmM2VYK2V4djhzNE5TZjY4WENMRXd1aHhoR2lMMkl5TGhZU1NZTWVmeVhaWDRxS0FFQTNjWjZWK3QvaTRFemZBYXRneUNqWFkrdnNkV3NrUnBRcXZkNkZUbXQ5UWl6Q1p0WjdPU0UybFFPb2dsRmh2cFZrS0pYV3pJaytVNkhKcHV3bldMbk1mWjhNTEc4Umx1SWNmVXFtS1dwNVRZMjdLcXR4N3BVUW1oRjR3R2pSMTJtaThaU1phODZuZUNpTWtPZVRTd2ZzSFU0bUpxK3A3K0VBWXZYbmVLQitjRFBjamdJN1VTWEc1NlhiLytGSHJPNmp4RXBBNFFzbUk5VjNyTGRYUHZaeDlkalpJYXR4dis5MjVTWXRPVGQiLCJtYWMiOiIyOGZjMGE5MDhjY2E5NmVhM2IxZDQ3NWZiYjdlYzRlZDIxNmM3NWFjOGJiZWVhNGMzYjg5ODA3NGM0OGMwY2E5IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ4VGJxK2k0YXlqWk1CVlpDWWp0dHc9PSIsInZhbHVlIjoiaEtxT3pLNVl0SXlEbG1PQWVubDNHaFlDTEI1Wkp4TGhuNW8ydWFNc1ZGcm1nNnBlK3c2UER5MzZOLzdpQjVVQmdWc1g1R1MraEhKZnBCQ1ZWczNVTUI2N3B1ajlBaHFsdkNOamhTc2pUTStJRnJhRm9TeDlNekh6WUZ4NklQUk1mMkt3Vm5GZE5IOUhDZFREQk5xMUhGTklCc1VZT3Iyblc2bTRMaG5Vd3NkcUpIbGNoRWFIazBPdGMwZmhEY21DY1l0aTY5QUhua3Vyc3VVVlJUMDh3VU44T0RyVGs4MnMyaFhVek5hZXVHOVR6M3I5b1VwVUxkN0F3MXI2ZkFGVlZpRGxENDRFMGJVejJZUUVEekZXRzRlWjR3SklZVUY5bk41M1hCS0ZxWC9zQmVpa2RoVkZ4Y2x2S0VmYXAzWDUzN0labGxrbXpRWVJ6cXFBVFM3WXNta2lTY2NOK2hhcEpnWWNUUjdHcEVEL1Jkb01vTktHOGk5b2JGV2ZrekdXemtwc01lUUY0NXUzMzhmNEVsZk5hb0h5cHk1b1VPZ0VLSVlYTytiU1o4QzJ3M0tyY0VrT1lMRlVtUXhiMHEyMnROdno3R3J2NllIc05vR2V2SE8zOEtDWVlFaFk5TUJwTWlQTDZQV0JLY0NrZDlQOHQvNXhDbVl2a3dQVUdqNXYiLCJtYWMiOiIzOTU3NzFmNzdlMmEzYWJlOWZiYWI2YTIxNjdjN2FiYmQxN2I1YWNmOGUwZTRlMWYyYjEwNTQ1OTQxZDY1YTI4IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtrRmt6czkzOFZCUkhhM0x6U290SUE9PSIsInZhbHVlIjoieXNGTDVBcHpxVTAvcU10RkNVK0tKMGdoZEhjVU0wTTl5VEl6WHp6YU5PMUNRSXVhdm1oVjNXd3JaNDVMU2NnNzZyRVM0bnhiVFBnTzMxekVCZ1JnMzRDeGpOMUUxTmRZZml4MHNQYmdQa2ZGcERTRXJqQlpOWHZrNXhwTkdSVFV2bmdPaWpkS2E5cnNnN3lxdy9ybXMwYTFxUUNKL2hXSk1DdmZwTWkwM0NHS0NmNkJpZE4rZXZnZVRkZ3p6SEtvUWk0c092aFUyeVVpWVVrTlZ0azBQTWNNcC85cG9KeVA2QTQ1WGVmM2VYK2V4djhzNE5TZjY4WENMRXd1aHhoR2lMMkl5TGhZU1NZTWVmeVhaWDRxS0FFQTNjWjZWK3QvaTRFemZBYXRneUNqWFkrdnNkV3NrUnBRcXZkNkZUbXQ5UWl6Q1p0WjdPU0UybFFPb2dsRmh2cFZrS0pYV3pJaytVNkhKcHV3bldMbk1mWjhNTEc4Umx1SWNmVXFtS1dwNVRZMjdLcXR4N3BVUW1oRjR3R2pSMTJtaThaU1phODZuZUNpTWtPZVRTd2ZzSFU0bUpxK3A3K0VBWXZYbmVLQitjRFBjamdJN1VTWEc1NlhiLytGSHJPNmp4RXBBNFFzbUk5VjNyTGRYUHZaeDlkalpJYXR4dis5MjVTWXRPVGQiLCJtYWMiOiIyOGZjMGE5MDhjY2E5NmVhM2IxZDQ3NWZiYjdlYzRlZDIxNmM3NWFjOGJiZWVhNGMzYjg5ODA3NGM0OGMwY2E5IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-124579387 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124579387\", {\"maxDepth\":0})</script>\n"}}