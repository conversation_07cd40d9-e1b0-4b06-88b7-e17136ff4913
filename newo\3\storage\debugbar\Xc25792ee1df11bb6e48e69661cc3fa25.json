{"__meta": {"id": "Xc25792ee1df11bb6e48e69661cc3fa25", "datetime": "2025-06-13 08:58:32", "utime": **********.892135, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749805111.685685, "end": **********.892188, "duration": 1.20650315284729, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1749805111.685685, "relative_start": 0, "end": **********.729039, "relative_end": **********.729039, "duration": 1.0433540344238281, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.729065, "relative_start": 1.0433800220489502, "end": **********.892195, "relative_end": 6.9141387939453125e-06, "duration": 0.1631300449371338, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44184656, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03053, "accumulated_duration_str": "30.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.807205, "duration": 0.02798, "duration_str": "27.98ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.648}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.855157, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.648, "width_percent": 3.013}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.870285, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.661, "width_percent": 5.339}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1626714968 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1626714968\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2132700014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2132700014\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-443637992 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443637992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2095252522 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749805096447%7C8%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRkVmtLU0VzMXJrK2FLMC94U1pQSWc9PSIsInZhbHVlIjoiUnVvcmxHUWpRMmQwYVVEbE4zVTgxcnJUTks3WTZpWUVudFJBQjEybUIzdU5kcjEwNCsvcnFlT3o2VW12MEpIZitYbXUvVEg2TDBTQnJkMDFuRVlaY2NqMTViVkpGdGZYdCtPK2pIeDNaOER2c2hYcEw4c09BaGFqR2o0aDJNZEJMS3lMR1g0Q2d4WWp3TjRpRjc4enF6QTZDUkFmVVhQYUdWMzEyV0tkWjdBaWUvNnoraDBDNFg2R0NiWDl5TFYrNUVDZmxXbmkvYnorSDlMNmtCaUorRlFIMU1IWkQ2TXRqdzdiTEpqak9VbXd0YmgvRnZFL1A2MmdPTjl0MnNrNjJ6OG45UXRtVXZsQWdpNW9xUzRjQWFOaE94cjhETXEyY25qLzljWnBwNTE0M3IrU3I5SElqQUkrNmpJVzFDVVZCZzg3TzE3RTlBb09Ya2xEMVBuK3VJMkIwUDZSZ2tZV0FDRGREWW45QVZvQVQwQ3E2bC85d1dMUmtNRmNqOWZwUVRpaXlCeEtKOXh0WVVpdmEyaUhTaHJBb3h4bWJrZlVweFNYRTFYYmlud0xLRFlxWXNpNmNlbTQ3eHBxVXNCS1puNHlyYzFveHNHaTVleTFGOGtvUkxzNE1jMVp0UmNZZWhOeGcxWUJUNytEeVpjTzZBNkFWNTRZbE42eWhkTkMiLCJtYWMiOiJlYTg5ZjlkMDI3YTg1OGMyMTY0MDMzZDI3OWJmZWZjMTY0ZWEyNWQ5M2IxZTg3NjRmNDc1MGQ3MDQxNzBlZDk4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InQ5TGRqZUEvVGI4NlRrOS9aOEJTRlE9PSIsInZhbHVlIjoiQnFhMGVuZzhkODBWSWx2MzNwZlF2VlgvOSt3Q21HWmE5aitWZjZ0QjFzU2lwU2VpMEZMcjJOVWxFbWVONm5lSGplbmRlNmhHUjdsUDh4QVlTMnU1cWY5MXQ2U0NzV3pNRUk0LzJoSnpKcTJIdHF4aTczUENoOVZDTnkwelpQUlpXSkJxeGtPU2NVbEVJdXJsZzhFQlNTS29uTWZESllmTklKTElZTjhXTmhXRm41RmVON0lUem43U0liVGNBN3JQc3NzaElWM3dmYUtuTEZyNUd5RnVqa2RBVSt2RnhENGxwZSt0Y2RHUm1JanZOMjIvUWlBWGxURlZVVXVkdFNhQ2RuaytocXdXVXdzM0w3aTNtbEFoMVYrQU0yZlFhN0NrSVEzN2piSWFzUndocG04bW1MbkhXK2lZM29nd3REU0szUGN3R3cyVkhYQTFtYjRGM3hEUGFvNlhVWUwrSXBJYkNjL1QwUUQrWTYvVHcyUTl0T284N2VVYW9ObVZ4M0UxdEhtRytaRG9jVGgrb2s5ZGFVK21xeko3VTFMK1JtTDk5REZ0N001a2E0RkF6Qjh0SmdRQWFjTkdjTE5rLzc1YUZ0WnRWMzdWcVp4NGxDWmU5cE01dS8wTW9LRldoSEFTNXlXTmFKUjRiem1NallEVFZZdmVFK0VpY1hTbjdLYlIiLCJtYWMiOiIwY2RlMmE5NjNkZDA2NTJjNmVmMThkZWU5NDA4NTRmYjQ1ZDk5MzdhOGJkMjk4NzYwN2JiNGM2Mzc2MWYzZDVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095252522\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1764403446 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764403446\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-435425828 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:58:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndOVHFMeW1ZWmFiaysrNXZDOU1OZVE9PSIsInZhbHVlIjoidDhCQlBhbmx0ZytjbjczQUh6NkJKaDVWbGpwSEUvNU9jblgrWVVXS0hxV0dGWmlISmplenBvZXR2VXVvOHZkbVJTazFZZWtXR0FSVFhQU1FkWG52THFXSEE1QmlnLytNUUhCU3hJUWMxRUtOdHA4NzFmc0pHa1NTTUFGdVpUdU4vUlNpVzFLeERaZ0lvOVlSYlRseFJwbzBCMDkrdzB0Z20yRzcvcHo5UWxybFVSZUFEMlo5U1EwZGxqbnk3K0FsUWdMejlYdUlWTEM3OHh0RnlDTkx3amY4V0xuT2lDSWp5ZHI2eUpRWkwxeUJ0VlpoYXFHdjAvRjNVVkdsMmlSdXllM1BlYVFGWmFZcUw0dk1CVjNiTGh6NnpXbFpEZ2x2MVpZbm1FOFdNZy9BTWlmNThidWxqei9EUVZWeUJkbFIyVGpHVnhieHdQQWFGVXZuTlBndnZCR3dkNE03ejhSaVpSOExHQzVWcnRmMTZYbjI1TWVIRG9JQ0tXbGNjaWEya3FtdXpqM0dGUEdRNUtNVFN2K0VySFNJakNURVVhMzRlNkxmQ042VGJLeXppWjVXYitBa0lKVUZCd2hIK2FqcE5uTFhTQy9MY0twdzVYNmZjTFBXTkVaZlJiRWdOK1ZYQ1c0UnJ6dWNKMjFZNk10WldaRmtybzhiTENkYTRCcTUiLCJtYWMiOiJjYTJhYzUwOTZjMDk2YjMwNWY2ZjRkMzVhOTk2MzkwNzk3MDM2NWI3MDE5YThhMDg5ZjZmNTEyMTIyZWZmNjYzIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InU5NnlwVUZyN1NBazNGVUlqZVp4ekE9PSIsInZhbHVlIjoianpLR0tGK2NWa2ZTZXlPSnU5WXpWQW5tWnl6SjZxcVJ6QlF6ZUdLdXlBWkhyeFdNNllvVEJiRXA3b1k0V0lDYVhNcC9lSTRSWVI3eDBhMzBHbUZUQ3VsUm1Rb1ZRWnJ2alc5aWlIRSsvc2EraEIxZlY4MUZaTVRCaFhxcGpGZ1c4b3NNbytud1U3YmszdzB1L2xYN2FUR1BpZW5hS1Raays2SHRlTWtrVm5hNXJEZm1VT1hYYkQzVUcwOElFY1lqdnlpVlVJU00yU1JIdXNkb043Y2loS1pDTUNLU0tJd3ZnQWhHYm1VakV3UGd4Z1JpekNCaU4xRXlxRlpGTG56SUt3dTU5R21WanFjK2tvUUZiUmJDR2JrTjFCNHJtNUdYVlc0YnNlVS91aXVZWEhNQ2tSSlUzdjlRMEdVelkyN2RoTVJsUEdqb09zU1EwZUpmOFZCT3gzalgyWEdyMktaeUxHU3ppNncxekM3a09jdVo0ejFHMjMzdWx1YTh2aDlyalM1RlNBdGcyMXFwUzJoeGdRRkRQQnhlMVN0bytFVDIzOWE4YkJHcFJaYytjSUtyVVk2VUpPOU9NdlBTMEMyZlcxamp3RktmbXloUnZJeGlzNHBsS1QyN0NUdk9jMG5kOUlqbXBHQ0Q5S0VYRVlaa0NhRWN6RVBDcG5xZkU3K0wiLCJtYWMiOiI4NWUyNmFhMmQ4MWY3YWIwYjJiODAzYTJiZjNlMjk4MmY4NmJlMWEyMjFmM2VlZmEwYWRkOWZlY2NmNmIwMDkzIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:58:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndOVHFMeW1ZWmFiaysrNXZDOU1OZVE9PSIsInZhbHVlIjoidDhCQlBhbmx0ZytjbjczQUh6NkJKaDVWbGpwSEUvNU9jblgrWVVXS0hxV0dGWmlISmplenBvZXR2VXVvOHZkbVJTazFZZWtXR0FSVFhQU1FkWG52THFXSEE1QmlnLytNUUhCU3hJUWMxRUtOdHA4NzFmc0pHa1NTTUFGdVpUdU4vUlNpVzFLeERaZ0lvOVlSYlRseFJwbzBCMDkrdzB0Z20yRzcvcHo5UWxybFVSZUFEMlo5U1EwZGxqbnk3K0FsUWdMejlYdUlWTEM3OHh0RnlDTkx3amY4V0xuT2lDSWp5ZHI2eUpRWkwxeUJ0VlpoYXFHdjAvRjNVVkdsMmlSdXllM1BlYVFGWmFZcUw0dk1CVjNiTGh6NnpXbFpEZ2x2MVpZbm1FOFdNZy9BTWlmNThidWxqei9EUVZWeUJkbFIyVGpHVnhieHdQQWFGVXZuTlBndnZCR3dkNE03ejhSaVpSOExHQzVWcnRmMTZYbjI1TWVIRG9JQ0tXbGNjaWEya3FtdXpqM0dGUEdRNUtNVFN2K0VySFNJakNURVVhMzRlNkxmQ042VGJLeXppWjVXYitBa0lKVUZCd2hIK2FqcE5uTFhTQy9MY0twdzVYNmZjTFBXTkVaZlJiRWdOK1ZYQ1c0UnJ6dWNKMjFZNk10WldaRmtybzhiTENkYTRCcTUiLCJtYWMiOiJjYTJhYzUwOTZjMDk2YjMwNWY2ZjRkMzVhOTk2MzkwNzk3MDM2NWI3MDE5YThhMDg5ZjZmNTEyMTIyZWZmNjYzIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InU5NnlwVUZyN1NBazNGVUlqZVp4ekE9PSIsInZhbHVlIjoianpLR0tGK2NWa2ZTZXlPSnU5WXpWQW5tWnl6SjZxcVJ6QlF6ZUdLdXlBWkhyeFdNNllvVEJiRXA3b1k0V0lDYVhNcC9lSTRSWVI3eDBhMzBHbUZUQ3VsUm1Rb1ZRWnJ2alc5aWlIRSsvc2EraEIxZlY4MUZaTVRCaFhxcGpGZ1c4b3NNbytud1U3YmszdzB1L2xYN2FUR1BpZW5hS1Raays2SHRlTWtrVm5hNXJEZm1VT1hYYkQzVUcwOElFY1lqdnlpVlVJU00yU1JIdXNkb043Y2loS1pDTUNLU0tJd3ZnQWhHYm1VakV3UGd4Z1JpekNCaU4xRXlxRlpGTG56SUt3dTU5R21WanFjK2tvUUZiUmJDR2JrTjFCNHJtNUdYVlc0YnNlVS91aXVZWEhNQ2tSSlUzdjlRMEdVelkyN2RoTVJsUEdqb09zU1EwZUpmOFZCT3gzalgyWEdyMktaeUxHU3ppNncxekM3a09jdVo0ejFHMjMzdWx1YTh2aDlyalM1RlNBdGcyMXFwUzJoeGdRRkRQQnhlMVN0bytFVDIzOWE4YkJHcFJaYytjSUtyVVk2VUpPOU9NdlBTMEMyZlcxamp3RktmbXloUnZJeGlzNHBsS1QyN0NUdk9jMG5kOUlqbXBHQ0Q5S0VYRVlaa0NhRWN6RVBDcG5xZkU3K0wiLCJtYWMiOiI4NWUyNmFhMmQ4MWY3YWIwYjJiODAzYTJiZjNlMjk4MmY4NmJlMWEyMjFmM2VlZmEwYWRkOWZlY2NmNmIwMDkzIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:58:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-435425828\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400972084 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400972084\", {\"maxDepth\":0})</script>\n"}}