{"__meta": {"id": "Xc3169f9d30257bbec8466d717377e089", "datetime": "2025-06-13 08:52:22", "utime": **********.398315, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804741.252453, "end": **********.398347, "duration": 1.1458938121795654, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1749804741.252453, "relative_start": 0, "end": **********.240543, "relative_end": **********.240543, "duration": 0.9880897998809814, "duration_str": "988ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.240561, "relative_start": 0.9881079196929932, "end": **********.39835, "relative_end": 3.0994415283203125e-06, "duration": 0.15778899192810059, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44181248, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.021600000000000005, "accumulated_duration_str": "21.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3157768, "duration": 0.019530000000000002, "duration_str": "19.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.417}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3562932, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.417, "width_percent": 4.583}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.373503, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95, "width_percent": 5}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-533498735 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-533498735\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-759600173 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-759600173\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-637278024 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637278024\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1054030183 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804688174%7C6%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkM4MzdiMzJ6WElXMm93N0lqTjhBVWc9PSIsInZhbHVlIjoieHNQVGtJREVXRUpVSjRrY3ZRai9jODVDOHlOMVRoQW9hSFhlSUNnMnpHbE1GSFg4V3REejczV0tEMkpLOEZhVWJCSlFNVzZmK1JPTDhDcEt0QmpmVU1qSXYzRzY4SDVCd2dYazgzVkt0a2hBU1FZSnpoaDNFTGs5bStnQkN1a01uOE1JVzJ4elJiNjJzMlRiSklEQXNqcGlLWFNmdnJYeHcwdzVRd3FYSmM5YlpOZXZBNGhnaDNNeEwxMHJHTXpxY1lzYnVNbDZOY2xZREY3ZEdYVXBmbzVUKzhobDZqL216YmQyeHZkcUlNRWFKSnFjdFRWVnE5NlV2QkZDUHcxVjUzc2IxemYvQmNHMnQySnc2UjdHMG9pVUIzbTRVd0FmSGFjWG5maWVaeS8wWTB6N0dnQllabDd0QXJCWnQyZHA5WnJLdUUwRC80M2laZnhWUURvZVc1dUZrRXNUNzJkUjdydDdoY054aGY0WUJ3U0N1TkEzaTRYejFPL1Y2WXJKMG9PS2ZXL1lDWGtuYS9rUjJnU0JzODhVNmVZQ2pGdFA4V1AvbTVhV241dlozTWYxV0ZBS2JqQmx4Zy9saTBlU25nM1U3RHhXL0h4V1VXQ1owZ3FwM2t2MjBybzdtUXBlTXovRkQ5RmM4Uk1aaGtYSWF2Ry9Cb0dJQXFVcGdkYlYiLCJtYWMiOiJiOTg2MDBhNjcwMjU0MDM2ZGZiZDYyNzA0ZGVlNzRkMTE5YTY1YzNkMmMzOTJkZDdjZGFmZDMwZDFjZjFhOGNiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdjcG9FUEZ5Mi83VWlJeE1tdDhUb1E9PSIsInZhbHVlIjoidjFMalZPV0JYTEdKajd2Mll6QVZmOUtlWkd4K0dFZ0ltUjQ1Wks4VVFFSXdOakh5M1VHbnZFbU9NUU9FL2pCaGN2a3pMZGQzNFpUOGx5R1hHK25Ib21USzhuaDVueW9hcWRraitOQnVjQmg3M1dVbmJ3QXk5YmNSWmsrUlA3cnFlcllsNFMrTHZ5NVI5UG5yeXAwT0R4d3QrYVNudHZnbURIZ0tPOC9ZbUZESW5XanRnQ0hQSVREVTlNcmlidU1LU2xsVjQxQzRzU1BIcHo4L1BNeUZINXhFWGFyODBiR1hXVHZPUFpnWHVxbmQ0MXp1T1NlcXk2c1FEeWRxQTRJT2RJMmVkRUhsRE9XSGQwRHFMd3Q3azI3RjVHbUhsbWlRZUM1c0ovMzFIMUFaaEYrdU54N2UrT1RUUTRQWTlTWjJiYWRJYjVMbk1sMWp2WUtIeXlXSUdyV0Y0TFF3QlRzeXBpMDFRb1UxbzF2U1R6V1pRa3RQSEgrMVpZL3VEWGp3alpwVTh5MElJQjV3L3F4cEdjRTB2bG1USm9FWG8yczJTOWRYbzRraUt1ZlphQ3dvZS9qci9vRnphUnRLTWVia2JjWklQZVFkZjRldmg5OXdOSFFGYWUxaUNSV0NpYitiKzgzeVFPWFpwMG53ajV5ZDNNMDNKRzNjOEVUQ0ZZQUIiLCJtYWMiOiI2OTY5ZGE2YzJkZThlNjE0ZjFmNGEyZTkyZGNiOThiODQ2ZDU3ZWY4ODhmNTA4YzFmNWNjOWFiYTVjYmY5YTM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054030183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-897716304 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897716304\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-12149965 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:52:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllMSFFKOWIwYlRDcGEyV3ZTQk1SVHc9PSIsInZhbHVlIjoieWwxSVZYZTFxODcxWkROcDBBYnE0b0Z4alA1enYwSzVSV0ZGOFpmMUpMN05TRkR3RXlaUzN2TFJUUVlhVjJiVW90cWJTMWtTZkd2OXRBRnlkZitZQXNUMXBNNWZ3V0pSQmV0OEZHejZWLzI2R0lrZGJCLzFabW1KaDNpdHBGSFg4U3FVZWdnQlRtZ1BCMDY3UzFpMVJ1QkUyZW1kV3BnSmRNRHNiN2QvUEpZblVtdTBuYVI5ZkswMm9zdHVLWkZsTDlmQTQ1L3lYNWJFYjRaUEdHSEtSaTR0WUhMeWFNMlM2NmdQUHc1R3RGdmhPUnFEbi9CSHpQb3ZOZkZMQVV6ZFpWRjJueVBOVGlobVNZVzhnaGN6aEFXYVdFQlcxdFZXRG5uRVhOY1orOGtlR0YzaUpWQVE5WnNET2NIZTc5VFBJdS9Oc3hvazNlbnV1MlJHc2Q2dktQU3E0NmJlRDF4RStLdkhrc1F6d1Z2VWhHZ2xJS3NEY0FkQisrL2dKSjU2ZXpPK2JQTk1xSkVxcXVwV0VDaU94OUdBQnZmVjllTUt2c0xURHNQbytHQVphcjA2RjVMSDRQQy9CUTcwWGRwQTI0VDg3ckRsOThsNHRYZHNyYlNVMmtNd3R4eHpkalFhYXpQSXFCd1RTdnd3MThSR2c0UVlvbXJtYzJkRGJHL2IiLCJtYWMiOiI5OTZkN2ViYjZkOWEyMmFkNmE2ODEyYjVjZDM1M2MwNGE2Yjc3MDc0MGEzOTM2Y2QyYTYyNjIwODQzMzE3YjFlIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:52:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhiQVFobTcyZGZkd0dTd3k5SVAvT0E9PSIsInZhbHVlIjoiZFhHMEJkTW5pRHpHdEdZNXovN0V4cTlDS3ptQThFbzRwOVdJVGlTUXE4cDkwWm0wckRrTWlMZlhGenE3ZDJLZURVdFgraGpEKzBZVURmMHN2ZWZUZkw2UHJ6TWpodnBDTmRrS09YcUZWZkV5TVRTUHNocmVmanVENEZUVnE0Ync0Z0F4Z0R3a1FWV0xvWGkzaTh2U0IwVEtKbW9oVzNKNVYrR1NLUkQ4TnBkcFJDRnhWM1lCY1l0M3Y2MDdBcXl5YTZDR1N6TkhQd1kwTHFMb1lqcjRDdS9VcmswS2pKOUIxbWd3UjcyQ083VHZWY1Z5YzkxSGJzZFBUenRueWptVkxEN1BaMkhFU29JTzhFRmV1L1ZLb2xSUDhVSXlKQk1FNHQxelhaVnhDQWNxdUFsZUxsK0lMWU0yN0F2QzAvMXZUMHdqVXI4ZVRtMkFoQnJhTUtLQkJ1R2pIVzdaQ2dnQlFQTC90OTlETFV6Q0VBRktBR2lRYzVlSDltSTdHcVZSNWtLMEtKUzV1MzhSNDBlTzZtQW5CREFzL01Na1AzcDVNZldUWDRpRHUvOWFGQ2diWHYwNEpGelZ3NWF0QnBjb3JzQTZHV2M3YW96cjlVVEtnSGViZUZzblkyNUFCWHNaVFJIOUpaU2M0dUJSR1ZQVnN3REtWMnpwcTVLcEJSaEMiLCJtYWMiOiI1OWM3YWQzMjg0MDk1Zjg0ZGI2MjE4N2VkYzdhN2EyNGUxNDI1ZmI0MmY4YjAxYjljNzgwNTk5NTY0ZWVkNmI3IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:52:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllMSFFKOWIwYlRDcGEyV3ZTQk1SVHc9PSIsInZhbHVlIjoieWwxSVZYZTFxODcxWkROcDBBYnE0b0Z4alA1enYwSzVSV0ZGOFpmMUpMN05TRkR3RXlaUzN2TFJUUVlhVjJiVW90cWJTMWtTZkd2OXRBRnlkZitZQXNUMXBNNWZ3V0pSQmV0OEZHejZWLzI2R0lrZGJCLzFabW1KaDNpdHBGSFg4U3FVZWdnQlRtZ1BCMDY3UzFpMVJ1QkUyZW1kV3BnSmRNRHNiN2QvUEpZblVtdTBuYVI5ZkswMm9zdHVLWkZsTDlmQTQ1L3lYNWJFYjRaUEdHSEtSaTR0WUhMeWFNMlM2NmdQUHc1R3RGdmhPUnFEbi9CSHpQb3ZOZkZMQVV6ZFpWRjJueVBOVGlobVNZVzhnaGN6aEFXYVdFQlcxdFZXRG5uRVhOY1orOGtlR0YzaUpWQVE5WnNET2NIZTc5VFBJdS9Oc3hvazNlbnV1MlJHc2Q2dktQU3E0NmJlRDF4RStLdkhrc1F6d1Z2VWhHZ2xJS3NEY0FkQisrL2dKSjU2ZXpPK2JQTk1xSkVxcXVwV0VDaU94OUdBQnZmVjllTUt2c0xURHNQbytHQVphcjA2RjVMSDRQQy9CUTcwWGRwQTI0VDg3ckRsOThsNHRYZHNyYlNVMmtNd3R4eHpkalFhYXpQSXFCd1RTdnd3MThSR2c0UVlvbXJtYzJkRGJHL2IiLCJtYWMiOiI5OTZkN2ViYjZkOWEyMmFkNmE2ODEyYjVjZDM1M2MwNGE2Yjc3MDc0MGEzOTM2Y2QyYTYyNjIwODQzMzE3YjFlIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:52:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhiQVFobTcyZGZkd0dTd3k5SVAvT0E9PSIsInZhbHVlIjoiZFhHMEJkTW5pRHpHdEdZNXovN0V4cTlDS3ptQThFbzRwOVdJVGlTUXE4cDkwWm0wckRrTWlMZlhGenE3ZDJLZURVdFgraGpEKzBZVURmMHN2ZWZUZkw2UHJ6TWpodnBDTmRrS09YcUZWZkV5TVRTUHNocmVmanVENEZUVnE0Ync0Z0F4Z0R3a1FWV0xvWGkzaTh2U0IwVEtKbW9oVzNKNVYrR1NLUkQ4TnBkcFJDRnhWM1lCY1l0M3Y2MDdBcXl5YTZDR1N6TkhQd1kwTHFMb1lqcjRDdS9VcmswS2pKOUIxbWd3UjcyQ083VHZWY1Z5YzkxSGJzZFBUenRueWptVkxEN1BaMkhFU29JTzhFRmV1L1ZLb2xSUDhVSXlKQk1FNHQxelhaVnhDQWNxdUFsZUxsK0lMWU0yN0F2QzAvMXZUMHdqVXI4ZVRtMkFoQnJhTUtLQkJ1R2pIVzdaQ2dnQlFQTC90OTlETFV6Q0VBRktBR2lRYzVlSDltSTdHcVZSNWtLMEtKUzV1MzhSNDBlTzZtQW5CREFzL01Na1AzcDVNZldUWDRpRHUvOWFGQ2diWHYwNEpGelZ3NWF0QnBjb3JzQTZHV2M3YW96cjlVVEtnSGViZUZzblkyNUFCWHNaVFJIOUpaU2M0dUJSR1ZQVnN3REtWMnpwcTVLcEJSaEMiLCJtYWMiOiI1OWM3YWQzMjg0MDk1Zjg0ZGI2MjE4N2VkYzdhN2EyNGUxNDI1ZmI0MmY4YjAxYjljNzgwNTk5NTY0ZWVkNmI3IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:52:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12149965\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1506275069 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506275069\", {\"maxDepth\":0})</script>\n"}}