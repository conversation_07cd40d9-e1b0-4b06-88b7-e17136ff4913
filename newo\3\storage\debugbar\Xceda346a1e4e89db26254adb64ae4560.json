{"__meta": {"id": "Xceda346a1e4e89db26254adb64ae4560", "datetime": "2025-06-13 08:51:27", "utime": **********.479702, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804686.414331, "end": **********.47974, "duration": 1.0654089450836182, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1749804686.414331, "relative_start": 0, "end": **********.323848, "relative_end": **********.323848, "duration": 0.9095170497894287, "duration_str": "910ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.323883, "relative_start": 0.9095520973205566, "end": **********.479744, "relative_end": 4.0531158447265625e-06, "duration": 0.15586090087890625, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44183864, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00768, "accumulated_duration_str": "7.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.407582, "duration": 0.00542, "duration_str": "5.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.573}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.438751, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.573, "width_percent": 13.932}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.459454, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.505, "width_percent": 15.495}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-168211848 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-168211848\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1059158014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1059158014\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1949506127 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1949506127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1094600797 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804675934%7C4%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpHSVExYXFSSkRzOCtubnIxNEpXUVE9PSIsInZhbHVlIjoiSTRyY2VPOXcyemh5N3FHNmwyR0dpQU1QWllEQTlJSllVdk1RNFZZbnVEYVgyNEhTY2lTaHRyYTJzUUVLem1uV1BoM0d5ajF0anlKdXVvYWRxb0RKQXFsL09nN3picmdxcUhQTWszamw3MktrMUFaSjRtWHZ3VDh1V0ZuZDFXeFhaanhWVFdvcWhBQ0R6MTFGNzFsSkw3QUxIZldEaFJmendGTkJHQ1B1TWRnSXZMbWVlZFBDc3dZWWt0ZWovejIrcEZOMEVPSWRTVERYbDd6Yy9yMGp1ckNJNGdVampiSkhLRXFJeVlwcXI3KytGZzMraFV1dC9idmVjZWIzNHBHeG9nSm5ud0ozY2dOUHlySVNzOEhYYmM4aG01WVRXeVBldVkyN3l2TXJyc3grVmoyZ0NReVdWenNMNjhQaGQ3SVJnVzE2SngxcE5CWFZTU2t2TFQ5TWpKWU9UR2NtM2J4bjhVNERRanJvRXdOMmdTREdzbHJwMkxjS1E2RVNsYnF4RXlWTnlkcUV6U1FnWlJ1Sk02c3hNRUlRdWR5Njl3SlJZcEJOTUlKMTA0dEZuRUVXT1o0bFE1emt4SHZycFlhU1haalRNZzFYWGdrdkNNRzZHV2dGd1M4TnlwQ1REQUNoREROdnZINkpzdlJKa0FKRUtaTzNiaWY5ZTBvd3QzdkIiLCJtYWMiOiJjOWVjYjRlOTU4YjU4MjVjYmE1MjFiODMzMWViN2QwMTczN2E4MmZkODRlZWJjYjZlN2U4MWViNzRjNmY2ZDBjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im55M1dQWmdHWU5PQldKUTBLdGJQdnc9PSIsInZhbHVlIjoiWDN2dVhpblZDNGpVTnFMVTVhZDJQK3dROHJXdVJReUJSdURuSUU0SDltTHhVMmdJMEJOSG4yanU0ZlhUWEV4SDMxd1JScDkvNHNSOUdyZFhMbkZGKzlEZlFzV0xlSmtoQTlseGhSOVMyWG1qVHdpN0piRFY1S0tPczM2WitFQ3pweERFOTBhZy9ZOUlrckQ5eEwxK0R1UlVzeEtjVnRXSFB2QUd5ZnBHWnhOMmVBV0ozYVBVc2tIemJIUHJ2U213NHFkQlBOSlBUTTBROGt3YzAzRnp3OTFHQ3VRaDh2bE5oOCs2c0xxTWhxTGs5cTl6Q1MzaTF2QVpNdEpzakVNMmVVdzJYRkRWR0dMUXBQbVBUbTZpYmlyUFJtbW9MdExKNW1WQ1E5ZE8wN0cxZ1VMK0g2Z25aYVducklLaEJqQ3NteEFsc3l5WTZyNmF1ZU5LMmRBWGNBODV0aXNtcnNsOUQ4WU9FakhzOVNMNTZ3cnllZzQvWWNsYzNBajVya0VBUjRka0k5Y3V5NVVET25uNG9HQitNdUtPVjZyZkRPL1FFckZiOXpXaUpOUGhkZXhNUFMwa0RFZ2JWZ25WTk90cU90dG02K0Yzd3FLNjNka1owMEIwU1psNVNjMnhieXRyVWFBTmFWOS9WTEJwOUZWWkQxWmhlaVZMYkNyTFNIWm8iLCJtYWMiOiI1YWU4OTMzNmNhYTJlNGQ4OWQ4ZDFiOGFmMGJiM2JlNGZhYzg4MDM3ZDViZmM1YzlhYTAzYzA2MWE0MDViN2M1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094600797\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2125864862 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2125864862\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:51:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlNdW1QdWl2VWlwYWx0dUM4V2xGTnc9PSIsInZhbHVlIjoicVdOOFJPbkQ5OHZvMFZ1cDV5QWNMbUJvS3JEbXc2Ui84R0M4ODI5aytXWnlKc2NadlVUS2Mrc1IzTGtvbzNTY3dQNWRGWHRuK0ZqNXVTOE95alpPQmtKMW05N2JZY052V05xM0tER0o4UVdYV1VmOXE4MDZnT21FVmxNajV1VTJNQUdlNDZxek4wRklBLzVKZE94QXFhT01PTUdmZWVzczZDYjBVSTYzSWJRQ2dudms5TVlqTmxZa3RVTWlTSHRteEJ1NkduMlhvUGovbmUxZ1JUNDNQSWUrZFdzYUpPYTMyUS94WlI5a2lpNWZiZlhqZVJVTXFQcGtrTDR6VVJrTFUrUktoRUNpM09QZE9STHozNjZxdGJGV1haaHYwM3VIQnVDMER3QW5Xcm9DUXl2RmJaMGY2c0JZdEpWV2lUMVVkc3JNd2d3VkRSbzJlQXRiZUF3K09mQWpwWXRHS0F3emJYbkFkb2VHZ1h1UVhlZ2EzeVdBVGJra3RBNGY0UXdWSEh5bFhlZy83aGIzTG5icUREb292bXloRkQ4aUJONjV5NWxwS2c1SUV0WWtuamhQalVpc3QvL0p2REJ6T1FtRDF1UVl4WWcxUDJqSWE1biszNXJhQnh1cXJhVldFSVhoT25LM3FRV01QNTU5V2w3dFVSS3pvVk95bHV5ejBGQjgiLCJtYWMiOiI3Zjg5ZTdlN2Q0ZGJlMjFmYjZhNWZjYjQ4ZTRkYjhjMzMwMDExYzRmMmM4N2ZkZDRlN2Q0ZmQ3ODYzMDFiOTVhIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjY3cjZxa2xGM0VTUWZ4dDNwdDZhK1E9PSIsInZhbHVlIjoiTEdHUFZpL01kZ0NiZjF5YWc0eVA0Tk93Qzl0K1gvRklZbW9sOVkzM29hV1RUWDBqU3JKbEFsM2NoSG1NLzRrOThHZnVEMUd4TXU5OW9yblBDUlJmM04wQkZ5N3Fhczk1NjR0REVENUFyY0UrS01JSmM5blV1OUQraG93bWplRzIzU0xIL1JjU0FZaFZlZnRxR080ODhNdk5Zbk84dEhleVI4dDZJS1Q0dEt3V0d0YlVjQWdrRFJWWkhlZFVNNWZmckJ3OFFUVkxtaXhRdVUxM2NQaXh0N0g0TDViaGpOYnBtQlIrWVhJRnZnZ3lFZG8zV095RUNuUmhNZ0ZqZXpBNUxvMldpQldPcGZGa1VjMm1YR2IzODhQaWlnQ1pSNHdrNnM3Titic01yQXArVzl5anVqVnJQdStBVFgvNURTMkFzbG9PUFZmQjk1dExXcWhmeW5YNXRMdEtnSlMzWUExQnJSL1F1OWZ1ZEVhVEVyM3J4azM3c29aTEJsN0NERHh0Ly84SytuZnA0U0xqN1pnYUVZN0hCYisyTjE0c0lPMXE2NExlTnhJSE93SVhnbnNiZEFkU1RSckRUOWtNL0d4bnAza3RPODUyNEsvRVBYU0w2aHgyZ1lNQXhoWUg4RU5VK0lHSHV0ajdseDNibGRTWmpHWHdzbHgvMGtpMzcraTciLCJtYWMiOiJkMTgxYWM4Mzg0ODU1ZWU5YTJlMTQzYjViOTc5MDhhMzVjOWYyODk2NjdiOTRiNTJkM2JjMzVlZjZlY2VhNTdiIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlNdW1QdWl2VWlwYWx0dUM4V2xGTnc9PSIsInZhbHVlIjoicVdOOFJPbkQ5OHZvMFZ1cDV5QWNMbUJvS3JEbXc2Ui84R0M4ODI5aytXWnlKc2NadlVUS2Mrc1IzTGtvbzNTY3dQNWRGWHRuK0ZqNXVTOE95alpPQmtKMW05N2JZY052V05xM0tER0o4UVdYV1VmOXE4MDZnT21FVmxNajV1VTJNQUdlNDZxek4wRklBLzVKZE94QXFhT01PTUdmZWVzczZDYjBVSTYzSWJRQ2dudms5TVlqTmxZa3RVTWlTSHRteEJ1NkduMlhvUGovbmUxZ1JUNDNQSWUrZFdzYUpPYTMyUS94WlI5a2lpNWZiZlhqZVJVTXFQcGtrTDR6VVJrTFUrUktoRUNpM09QZE9STHozNjZxdGJGV1haaHYwM3VIQnVDMER3QW5Xcm9DUXl2RmJaMGY2c0JZdEpWV2lUMVVkc3JNd2d3VkRSbzJlQXRiZUF3K09mQWpwWXRHS0F3emJYbkFkb2VHZ1h1UVhlZ2EzeVdBVGJra3RBNGY0UXdWSEh5bFhlZy83aGIzTG5icUREb292bXloRkQ4aUJONjV5NWxwS2c1SUV0WWtuamhQalVpc3QvL0p2REJ6T1FtRDF1UVl4WWcxUDJqSWE1biszNXJhQnh1cXJhVldFSVhoT25LM3FRV01QNTU5V2w3dFVSS3pvVk95bHV5ejBGQjgiLCJtYWMiOiI3Zjg5ZTdlN2Q0ZGJlMjFmYjZhNWZjYjQ4ZTRkYjhjMzMwMDExYzRmMmM4N2ZkZDRlN2Q0ZmQ3ODYzMDFiOTVhIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjY3cjZxa2xGM0VTUWZ4dDNwdDZhK1E9PSIsInZhbHVlIjoiTEdHUFZpL01kZ0NiZjF5YWc0eVA0Tk93Qzl0K1gvRklZbW9sOVkzM29hV1RUWDBqU3JKbEFsM2NoSG1NLzRrOThHZnVEMUd4TXU5OW9yblBDUlJmM04wQkZ5N3Fhczk1NjR0REVENUFyY0UrS01JSmM5blV1OUQraG93bWplRzIzU0xIL1JjU0FZaFZlZnRxR080ODhNdk5Zbk84dEhleVI4dDZJS1Q0dEt3V0d0YlVjQWdrRFJWWkhlZFVNNWZmckJ3OFFUVkxtaXhRdVUxM2NQaXh0N0g0TDViaGpOYnBtQlIrWVhJRnZnZ3lFZG8zV095RUNuUmhNZ0ZqZXpBNUxvMldpQldPcGZGa1VjMm1YR2IzODhQaWlnQ1pSNHdrNnM3Titic01yQXArVzl5anVqVnJQdStBVFgvNURTMkFzbG9PUFZmQjk1dExXcWhmeW5YNXRMdEtnSlMzWUExQnJSL1F1OWZ1ZEVhVEVyM3J4azM3c29aTEJsN0NERHh0Ly84SytuZnA0U0xqN1pnYUVZN0hCYisyTjE0c0lPMXE2NExlTnhJSE93SVhnbnNiZEFkU1RSckRUOWtNL0d4bnAza3RPODUyNEsvRVBYU0w2aHgyZ1lNQXhoWUg4RU5VK0lHSHV0ajdseDNibGRTWmpHWHdzbHgvMGtpMzcraTciLCJtYWMiOiJkMTgxYWM4Mzg0ODU1ZWU5YTJlMTQzYjViOTc5MDhhMzVjOWYyODk2NjdiOTRiNTJkM2JjMzVlZjZlY2VhNTdiIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}