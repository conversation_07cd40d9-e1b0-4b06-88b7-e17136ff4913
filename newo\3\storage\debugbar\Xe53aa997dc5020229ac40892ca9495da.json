{"__meta": {"id": "Xe53aa997dc5020229ac40892ca9495da", "datetime": "2025-06-13 08:50:02", "utime": 1749804602.904707, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804594.844625, "end": 1749804602.904734, "duration": 8.06010890007019, "duration_str": "8.06s", "measures": [{"label": "Booting", "start": 1749804594.844625, "relative_start": 0, "end": **********.015718, "relative_end": **********.015718, "duration": 1.1710929870605469, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.015769, "relative_start": 1.1711440086364746, "end": 1749804602.904737, "relative_end": 3.0994415283203125e-06, "duration": 6.888967990875244, "duration_str": "6.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 42578144, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 6.72909, "accumulated_duration_str": "6.73s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.141227, "duration": 6.72909, "duration_str": "6.73s", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zTHXXS9JeppUWUVKEDzCvqEIACaxlxrCBZnAWC85", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-162047132 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-162047132\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1530847399 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1530847399\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1023061506 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"194 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwo%7C0%7C1960</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023061506\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1660609076 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660609076\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1637655907 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:50:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpHM0tkZ2J0c3RRaHQ0c2hlMFlnZ2c9PSIsInZhbHVlIjoiZk9XYVErZ1RpTC9XTkMvTXRXWjhOaXRKUlVKVUo0eXdLTzlDS0JxcEtyOThKV3N4aldUSzB4SW1odXpxUlJUdldkRGRPcUNOVzYzWTFTT3paZG4rOGtIcHFCc2l4YkY3UnByNXZEYlRQK254Q3RLVityU0JFQThkSExmNi93TGlqMm1kSEpNdTF2eWVBQisycjZKRHFvS3FUalVGMzF0VUN2VkdqSVpnUXVQenlNYm90dkFxekRva0hPRG5kTVJ3M1hwb1NkdDFyR2d6WjMwNnAwckpTZk1UdjEwa3lrZ01aN2Uxc2VJSUQ0YlMrSUNKZmVpN1NpTVRuM09NTyt4NFZGN09wbWhkN2xGV1BEeVRhekFVVlJTc3B0emZ0cDJabmZSdmo3TFNwaW9xdG81eVk3UCs4ckxwU3grTXduOEx1YnlFNC9BckRsYTQ3d3ZmMDNOWk9KL1FlREtOaThSMHRzeUlMSzlkZC9hb1E5NllMZTVaZlFZNlhBMTNrS3hnRGg5bjZCWXJkMVNVNDdhQkpFMkY1cXhsOERUSVJ2cFp4N3UrMkdLaFVQWVlXTml5VVFGZTRQUWhTcHNQQ0ZGV2dDU0xKVVVvRjkwRklUSHYrZzk3VXdaVHRLb0tEQzhNNGNnMDI5aGJJQlRJL3dneUpGUXRSbG5TaHBveG54UkgiLCJtYWMiOiJkYWVlZmIyMWUzMzdiZmIyMzNjZmIwOTQwMGYzYWUzMzhkOTA3NjEyZmI5MjcxM2VlNmZhZDk0ZTkyYjYxODQwIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJVOHoyWnRzWFZqN1N1UWt4bWphaXc9PSIsInZhbHVlIjoiaXB5Z2Q3Ukd5R0JWSWxEb2V6bm5WQ0pMREFub0plS2M1VWxjaXpjR3ZrTnp4WDRnSDJHVE42WHVYcVNhTXVOZGdGV3hMK0FaZndGVnpSUGZMakNRY0lNYXluVWtOVkxhZFFNbk1INC9nWWtNRS85dmxycHV6VkU0VTNnOFJwTXA3R1RzWlpBR1V1Y3N5Z2RPc0IrekdkNjNkdS8wYTdpekNiYks1S0JpeDJxRzRBS3RLMWViOElIVitETHFXbHl6RkJ1N0FQNndubmx4QjgvSExlN3F3RE1Zbmw3TFVxVjYxbkxST2xPVTVIbEduWkZRelFpdVV6ekdRVUU3V0craC85MmJyZU1GWnkrWVVWRHNOUXhrSE9YRzBNVUFOb0JEZm10amdwRkthdWs0OG5PMjU3N2xQT2U3alBzWXdEN3BQVG5WZGFuOGh4azlScEllMGMzcFV2THFzMDdEUVE0ajVENVlwZVNhc0NObERhdmxUN3E4T3NtWnlJSWozSnBSREpKeHFCYVZvQW85ZzdxSU9VZXZQUXZUd2hxemN5cDM1czhwMnM1dk43bjU3bjZFT0ZUN2h4bE1uQ3Y2SWppOHlydmcyTU1ORjQ5UnRhenhnQkFIczBRckRoSitKUkw2UC9nRHhBQnN5VUg1dmJ5ZFR3cnlJNStYZDl2V05xNXQiLCJtYWMiOiJiMDRjNDcyOTkzNDA2NjJhOTQ3YzdmNjI2YjkyNTVlNzJmY2NjYzZjZGUwMjUyYzQ2YzBmZGI5MTQ0YjRlNGFiIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpHM0tkZ2J0c3RRaHQ0c2hlMFlnZ2c9PSIsInZhbHVlIjoiZk9XYVErZ1RpTC9XTkMvTXRXWjhOaXRKUlVKVUo0eXdLTzlDS0JxcEtyOThKV3N4aldUSzB4SW1odXpxUlJUdldkRGRPcUNOVzYzWTFTT3paZG4rOGtIcHFCc2l4YkY3UnByNXZEYlRQK254Q3RLVityU0JFQThkSExmNi93TGlqMm1kSEpNdTF2eWVBQisycjZKRHFvS3FUalVGMzF0VUN2VkdqSVpnUXVQenlNYm90dkFxekRva0hPRG5kTVJ3M1hwb1NkdDFyR2d6WjMwNnAwckpTZk1UdjEwa3lrZ01aN2Uxc2VJSUQ0YlMrSUNKZmVpN1NpTVRuM09NTyt4NFZGN09wbWhkN2xGV1BEeVRhekFVVlJTc3B0emZ0cDJabmZSdmo3TFNwaW9xdG81eVk3UCs4ckxwU3grTXduOEx1YnlFNC9BckRsYTQ3d3ZmMDNOWk9KL1FlREtOaThSMHRzeUlMSzlkZC9hb1E5NllMZTVaZlFZNlhBMTNrS3hnRGg5bjZCWXJkMVNVNDdhQkpFMkY1cXhsOERUSVJ2cFp4N3UrMkdLaFVQWVlXTml5VVFGZTRQUWhTcHNQQ0ZGV2dDU0xKVVVvRjkwRklUSHYrZzk3VXdaVHRLb0tEQzhNNGNnMDI5aGJJQlRJL3dneUpGUXRSbG5TaHBveG54UkgiLCJtYWMiOiJkYWVlZmIyMWUzMzdiZmIyMzNjZmIwOTQwMGYzYWUzMzhkOTA3NjEyZmI5MjcxM2VlNmZhZDk0ZTkyYjYxODQwIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJVOHoyWnRzWFZqN1N1UWt4bWphaXc9PSIsInZhbHVlIjoiaXB5Z2Q3Ukd5R0JWSWxEb2V6bm5WQ0pMREFub0plS2M1VWxjaXpjR3ZrTnp4WDRnSDJHVE42WHVYcVNhTXVOZGdGV3hMK0FaZndGVnpSUGZMakNRY0lNYXluVWtOVkxhZFFNbk1INC9nWWtNRS85dmxycHV6VkU0VTNnOFJwTXA3R1RzWlpBR1V1Y3N5Z2RPc0IrekdkNjNkdS8wYTdpekNiYks1S0JpeDJxRzRBS3RLMWViOElIVitETHFXbHl6RkJ1N0FQNndubmx4QjgvSExlN3F3RE1Zbmw3TFVxVjYxbkxST2xPVTVIbEduWkZRelFpdVV6ekdRVUU3V0craC85MmJyZU1GWnkrWVVWRHNOUXhrSE9YRzBNVUFOb0JEZm10amdwRkthdWs0OG5PMjU3N2xQT2U3alBzWXdEN3BQVG5WZGFuOGh4azlScEllMGMzcFV2THFzMDdEUVE0ajVENVlwZVNhc0NObERhdmxUN3E4T3NtWnlJSWozSnBSREpKeHFCYVZvQW85ZzdxSU9VZXZQUXZUd2hxemN5cDM1czhwMnM1dk43bjU3bjZFT0ZUN2h4bE1uQ3Y2SWppOHlydmcyTU1ORjQ5UnRhenhnQkFIczBRckRoSitKUkw2UC9nRHhBQnN5VUg1dmJ5ZFR3cnlJNStYZDl2V05xNXQiLCJtYWMiOiJiMDRjNDcyOTkzNDA2NjJhOTQ3YzdmNjI2YjkyNTVlNzJmY2NjYzZjZGUwMjUyYzQ2YzBmZGI5MTQ0YjRlNGFiIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637655907\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-82711893 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zTHXXS9JeppUWUVKEDzCvqEIACaxlxrCBZnAWC85</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82711893\", {\"maxDepth\":0})</script>\n"}}