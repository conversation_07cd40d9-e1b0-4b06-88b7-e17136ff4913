{"__meta": {"id": "Xe65adf2e7bfd5f8d2cb131d2bd42602c", "datetime": "2025-06-13 08:51:28", "utime": **********.691102, "method": "GET", "uri": "/roles/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804687.602693, "end": **********.691144, "duration": 1.0884509086608887, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1749804687.602693, "relative_start": 0, "end": **********.357399, "relative_end": **********.357399, "duration": 0.7547059059143066, "duration_str": "755ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.357415, "relative_start": 0.7547218799591064, "end": **********.691147, "relative_end": 3.0994415283203125e-06, "duration": 0.33373212814331055, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51753360, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.create", "param_count": null, "params": [], "start": **********.566659, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/role/create.blade.phprole.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Frole%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.create"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.660813, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/create", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.create", "controller": "App\\Http\\Controllers\\RoleController@create", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=31\" onclick=\"\">app/Http/Controllers/RoleController.php:31-57</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.022879999999999998, "accumulated_duration_str": "22.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4149091, "duration": 0.01502, "duration_str": "15.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.647}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4516098, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.647, "width_percent": 2.579}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4870338, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 68.226, "width_percent": 4.808}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.493754, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 73.033, "width_percent": 4.196}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\RoleController.php", "line": 45}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.504168, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:45", "source": "app/Http/Controllers/RoleController.php:45", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=45", "ajax": false, "filename": "RoleController.php", "line": "45"}, "connection": "ty", "start_percent": 77.229, "width_percent": 19.318}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.create", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/role/create.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.643782, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 96.547, "width_percent": 3.453}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 524, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 527, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1948628498 data-indent-pad=\"  \"><span class=sf-dump-note>create role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948628498\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.502907, "xdebug_link": null}]}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/create", "status_code": "<pre class=sf-dump id=sf-dump-670376885 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-670376885\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-58484011 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-58484011\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-909671298 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-909671298\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804686789%7C5%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJkZ3oveVh2VUNzRWp5Q3R2ODNzWXc9PSIsInZhbHVlIjoiWGlzUkx6cENuTDRVZVpDUCtjVFFiQVZZTFlsT1FkK0t6dUdyaDdhYUxKYnNGN040aE02WjVydmREQWFEVkhJWmxmdWExcmFhakVNekcwY0JnS3ZiSm55N0lSSEF2RzNmaC9YZUNBVldIeEZweW0yZDlWaFZQYUNJdjMxWlAvZkRrRHBLTjB4T05jYXpzWjk3ZTh6TlJ3RUpvRnFVTDZickZJOEhBRmgzZHpPY0dzMmNFUUloQUUzR0J4eVlOdGtuMWpCcWJhbndjK0g0a0pBUTNpeUY5OU5Ea3k3d0c5ZXJROGhyVE5tRXhBM2hpWVIxZjJDWEZURjNGVEdhS3VwK2xaODhWTjVRNWxRRUpQeGIrNlkvQ3d3U1JBa2o5ckFmWGRRVDZreDVYaUJTclNhaGZvSFYrcEZZUXFtMTMwYXdRci9yaFpySDJqRkJ3MnpIeXkrRGdTK1RqcExiVjZiQzlaczJna1NuNHhFdytPd2hTcnVEZFdSRE5VUGRFcEo2TmxtOVJaMzFBbXE2L1RodkhuTnNuT3oxN3dhcmlzV0NCMjgrYWRNdCt6K0crVTJrbkpGc0RGUEFUZEt2TE9UcnlZT1FMQythZm1TUWVWYit3eklVOFNnOS8ySlE0d0RGQm1TMi8rZWl0bS9KZXJxOWp1bkEwYkJhRGJRdkdqUmciLCJtYWMiOiI0OTY0Y2JjNGIzYmUxZmJkNTIyODdiYzZmYjQxMWIwMDZjODA4MTcxYjJkOTVjMjY5NmJhN2MwODU3YmMxYjBjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImVWeld2UlIwaDZBc2FjY0dKWGtVS3c9PSIsInZhbHVlIjoiY3FrbWRzbXhMOENXUXZtR3dyc1NzUk9ZcGtUNXQ0UFkwa0ViOStRSE8yVjVQQTBnRVlNQlliYVN2ekVvYkpDV1ppZzRySUJmN2ZBRk5mbWxEQm5QMWZLMDFENHdnYkNUUkRaMDYzRnRKaXJMN0R0Kzk3bzZwTDdNSWdCWFh0TkNRbmswaWZxSHU3cjFZdVZ2QWZCcmZSSDd3MXZ0ejlEaDdNYjY4bmZ4UDRvU29PT0t6OFVEMUFWSTNjc2dyd0pVbXhiTEc0dnkyU1h3YlhQN0NwbVhEWWk5ZEd6WXhyRnpZdVlaRGhmbWpmK1VBNyt1VGtBTmc2WUlrcmRKTllUU2c2MjZDYWhWUHk5a2JHMjVmZjYzdGZjdkVWSVhuSGY2ZGw1QUxwZGJJYmo0Q0lZcGdqUERkL1k1R0F6RjNvKzJKS2ljTjZnaTZLMS9PT2Y0ZjJ1SlV6bTc5QUlGL0wrNjIyb20vMFJldERuaGVLNHY1UEdIUWJXQzc2SEVlSjFIQ1Q2Y0Fmd01YYWJucElzZHpIYXdGTy80eTI0YVFjMmN1L290UVBXTW9JUDJuS2ZEQXZwam5aSk85Rk5YemY2WG9qRGRyd05EYmdKUkJiK0VaUFE0eWtxYkF0citTT3IxVWk3QmowYTU3QlBoczFyalVQem1oU0NhODlVMjhIczEiLCJtYWMiOiIyYmFmMDRhZDA5ZDA2NzM1OGUxZDg3ZTFiOGEwNDAyYWI2M2I3YzhkZDdiODJiODEzYTVhYjY4MTMxMTU5NzRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-629176657 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">msK083S1kfUnSrMauFb0VwPWpN4lJxSww5zAmDuf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629176657\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1084813338 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:51:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iks0aklSUUsxQW8xVFNuSGpIck96b3c9PSIsInZhbHVlIjoiVE1HUndIdEFlZ0xBdmEycFM4YVRYbTZ4b1dTVU1FRGl3Tkh0OU9yMUlmWnk5NHJ5WXk2dytiTVN2YktpSkt1SDBTeCtpK2N2R3hCNEQrOXY3MzFBSXZEYlBhakROcHJEc2FjTVZGQlgzVldxb2tMbXNZSjJhUjNjSnFIT1FMQkhzd1MyS25sRXlZUHQ0SUJCZ3RUSUcza0VxeVJGN1kvdm0rS3dPSUxTTkVlYWxVU2VITlk0eHR6cktYclE1TnQvMGRFK1F3SlRkeTBYMDI0YjdOeVR2ekhTYmY1Rk1HaDNTa1hGNUJ4cEpMY09OKzNWbHF5YTlPeXV0QnFRY29PREdlWHVUc2tCZjFPMVVxd25JOU55eVFtVjFmUG9RS3dsTFN2OFJPaW1NTTkzWjIzeHBwdkZYdHM3KzhISlFhRFpKNEYxSVNIZFE5elpyancxZ2ZaVWFMVWdOWDZiYVBSTndYMlQvZkZ3RjFhSHhpVDVpTWJPSnNkN2Y1bnR0S2I3RzEvRWMyWmVsZDJxTFJTc0hsM3NaRmo1V0wzRFcxWG9VNE5rT3RaZkc2NEFlVG0rdUkyREYreEJwSG83UUQvVDZWZ0JkTGpJaUM5cWtDS25qRjJwd0ZMZHR3Yi8zbDV1SkVzcTdqUUJQZXB3VVJwNmNGenJtQ2lJY0NWRFFtSzYiLCJtYWMiOiI3MDkyZWMyYmRkMDIzODhmNjljZDEyNTFiZWI0OTEyNjdhNjU1NWRhZGFmODFjNzNjMjYxZWZmMDk5YmIzNjExIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1hQVNPNmFSMzUwcVRoOHEzMVUxc0E9PSIsInZhbHVlIjoiVUJ0eExuaSt0c1RpczBsa1hobHJvMHlSUXoya1lNR09Yc3NnWGZMemlnN2ZPVWZqR0RYTGJHekh5Y3U0ZXRsT1hpWVFuSDhhcS9tVFAvbUE2S3JLQVoyV2NNbEF3RDR2VFZ3ZjI1LzAybW5MR1l0aHhZaE5GQnNGS2E2Y2M2T2JUR2xkSzROdHFKcmZZeGZNaFRhWDhHLzR1K1UxTlloMGkyUG5VTTRvOUlYVVM4Qk9ubkFHOG5qUGIrUnAzNjdlOExYclQzQnZ0eldlZUl0QVUyenVxeWZ2MS9CWmdFTW9lQkJ5ZDI3MEVTRzBiZ3BYVEQvSHMvV1FwK2lST0VERjZ3bitjM1BFOERSVm90NDJxQmlvcUdpSmh1dlRwZXlOSmo3akNoYStZNXlud0Y4emlCY3cwWUluOVB4d3R6MnFpU2YybzY1Rkh4TEplYWFOS1ZHN0V5ZkZTdWNJcEJvQzBCYXJKa2NQK3VObWlES0ZCN21pZTFqUG54MUQrUnVJVmw0Umx2NDAxRndObFNnVldWYkc5KzJIYTBUdWRLV1BkWE9jZWhFNmVnN1Fqczg4NVJtL2dPZHlOMm40UFVuY3RCYjdTc2F0OXVzNzJTK09LSFMrVTdja29WYWxhS2daOHE1bjR5T056ZzFyZWduQU9BbTYxMk1YTXB4ZW1lankiLCJtYWMiOiJjNzZlMzdjOGY0MTJjMzE3YmYzNjIyZDI0M2M5MTg1MWEyZTA3MTE0MmEzOGM5MTE5ODFhZjEzM2EzMTY5MTc1IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:51:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iks0aklSUUsxQW8xVFNuSGpIck96b3c9PSIsInZhbHVlIjoiVE1HUndIdEFlZ0xBdmEycFM4YVRYbTZ4b1dTVU1FRGl3Tkh0OU9yMUlmWnk5NHJ5WXk2dytiTVN2YktpSkt1SDBTeCtpK2N2R3hCNEQrOXY3MzFBSXZEYlBhakROcHJEc2FjTVZGQlgzVldxb2tMbXNZSjJhUjNjSnFIT1FMQkhzd1MyS25sRXlZUHQ0SUJCZ3RUSUcza0VxeVJGN1kvdm0rS3dPSUxTTkVlYWxVU2VITlk0eHR6cktYclE1TnQvMGRFK1F3SlRkeTBYMDI0YjdOeVR2ekhTYmY1Rk1HaDNTa1hGNUJ4cEpMY09OKzNWbHF5YTlPeXV0QnFRY29PREdlWHVUc2tCZjFPMVVxd25JOU55eVFtVjFmUG9RS3dsTFN2OFJPaW1NTTkzWjIzeHBwdkZYdHM3KzhISlFhRFpKNEYxSVNIZFE5elpyancxZ2ZaVWFMVWdOWDZiYVBSTndYMlQvZkZ3RjFhSHhpVDVpTWJPSnNkN2Y1bnR0S2I3RzEvRWMyWmVsZDJxTFJTc0hsM3NaRmo1V0wzRFcxWG9VNE5rT3RaZkc2NEFlVG0rdUkyREYreEJwSG83UUQvVDZWZ0JkTGpJaUM5cWtDS25qRjJwd0ZMZHR3Yi8zbDV1SkVzcTdqUUJQZXB3VVJwNmNGenJtQ2lJY0NWRFFtSzYiLCJtYWMiOiI3MDkyZWMyYmRkMDIzODhmNjljZDEyNTFiZWI0OTEyNjdhNjU1NWRhZGFmODFjNzNjMjYxZWZmMDk5YmIzNjExIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1hQVNPNmFSMzUwcVRoOHEzMVUxc0E9PSIsInZhbHVlIjoiVUJ0eExuaSt0c1RpczBsa1hobHJvMHlSUXoya1lNR09Yc3NnWGZMemlnN2ZPVWZqR0RYTGJHekh5Y3U0ZXRsT1hpWVFuSDhhcS9tVFAvbUE2S3JLQVoyV2NNbEF3RDR2VFZ3ZjI1LzAybW5MR1l0aHhZaE5GQnNGS2E2Y2M2T2JUR2xkSzROdHFKcmZZeGZNaFRhWDhHLzR1K1UxTlloMGkyUG5VTTRvOUlYVVM4Qk9ubkFHOG5qUGIrUnAzNjdlOExYclQzQnZ0eldlZUl0QVUyenVxeWZ2MS9CWmdFTW9lQkJ5ZDI3MEVTRzBiZ3BYVEQvSHMvV1FwK2lST0VERjZ3bitjM1BFOERSVm90NDJxQmlvcUdpSmh1dlRwZXlOSmo3akNoYStZNXlud0Y4emlCY3cwWUluOVB4d3R6MnFpU2YybzY1Rkh4TEplYWFOS1ZHN0V5ZkZTdWNJcEJvQzBCYXJKa2NQK3VObWlES0ZCN21pZTFqUG54MUQrUnVJVmw0Umx2NDAxRndObFNnVldWYkc5KzJIYTBUdWRLV1BkWE9jZWhFNmVnN1Fqczg4NVJtL2dPZHlOMm40UFVuY3RCYjdTc2F0OXVzNzJTK09LSFMrVTdja29WYWxhS2daOHE1bjR5T056ZzFyZWduQU9BbTYxMk1YTXB4ZW1lankiLCJtYWMiOiJjNzZlMzdjOGY0MTJjMzE3YmYzNjIyZDI0M2M5MTg1MWEyZTA3MTE0MmEzOGM5MTE5ODFhZjEzM2EzMTY5MTc1IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:51:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1084813338\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1221080619 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221080619\", {\"maxDepth\":0})</script>\n"}}