{"__meta": {"id": "Xf18d69cb24c7903078f650d2d2a76951", "datetime": "2025-06-13 08:50:04", "utime": **********.242472, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804602.922598, "end": **********.242498, "duration": 1.3199000358581543, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": 1749804602.922598, "relative_start": 0, "end": **********.618514, "relative_end": **********.618514, "duration": 0.6959161758422852, "duration_str": "696ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.618532, "relative_start": 0.6959340572357178, "end": **********.2425, "relative_end": 2.1457672119140625e-06, "duration": 0.6239681243896484, "duration_str": "624ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45159592, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.066035, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.087797, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.214979, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.225768, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.41175999999999996, "accumulated_duration_str": "412ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.671912, "duration": 0.02157, "duration_str": "21.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 5.238}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.699021, "duration": 0.30355, "duration_str": "304ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "ty", "start_percent": 5.238, "width_percent": 73.72}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.012675, "duration": 0.00738, "duration_str": "7.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "ty", "start_percent": 78.959, "width_percent": 1.792}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.069288, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 80.751, "width_percent": 0.243}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.089869, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 80.994, "width_percent": 0.175}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4081}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.117351, "duration": 0.07127, "duration_str": "71.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4081", "source": "app/Models/Utility.php:4081", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4081", "ajax": false, "filename": "Utility.php", "line": "4081"}, "connection": "ty", "start_percent": 81.169, "width_percent": 17.309}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4082}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4123}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1980588, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4082", "source": "app/Models/Utility.php:4082", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4082", "ajax": false, "filename": "Utility.php", "line": "4082"}, "connection": "ty", "start_percent": 98.477, "width_percent": 0.396}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\to\\newo\\3\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.20341, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 98.873, "width_percent": 0.182}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\to\\newo\\3\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.218287, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "ty", "start_percent": 99.055, "width_percent": 0.945}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zTHXXS9JeppUWUVKEDzCvqEIACaxlxrCBZnAWC85", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1974129792 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1974129792\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-419045451 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-419045451\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1314241940 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1314241940\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1405732399 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1930 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwo%7C0%7C1960; XSRF-TOKEN=eyJpdiI6IlpHM0tkZ2J0c3RRaHQ0c2hlMFlnZ2c9PSIsInZhbHVlIjoiZk9XYVErZ1RpTC9XTkMvTXRXWjhOaXRKUlVKVUo0eXdLTzlDS0JxcEtyOThKV3N4aldUSzB4SW1odXpxUlJUdldkRGRPcUNOVzYzWTFTT3paZG4rOGtIcHFCc2l4YkY3UnByNXZEYlRQK254Q3RLVityU0JFQThkSExmNi93TGlqMm1kSEpNdTF2eWVBQisycjZKRHFvS3FUalVGMzF0VUN2VkdqSVpnUXVQenlNYm90dkFxekRva0hPRG5kTVJ3M1hwb1NkdDFyR2d6WjMwNnAwckpTZk1UdjEwa3lrZ01aN2Uxc2VJSUQ0YlMrSUNKZmVpN1NpTVRuM09NTyt4NFZGN09wbWhkN2xGV1BEeVRhekFVVlJTc3B0emZ0cDJabmZSdmo3TFNwaW9xdG81eVk3UCs4ckxwU3grTXduOEx1YnlFNC9BckRsYTQ3d3ZmMDNOWk9KL1FlREtOaThSMHRzeUlMSzlkZC9hb1E5NllMZTVaZlFZNlhBMTNrS3hnRGg5bjZCWXJkMVNVNDdhQkpFMkY1cXhsOERUSVJ2cFp4N3UrMkdLaFVQWVlXTml5VVFGZTRQUWhTcHNQQ0ZGV2dDU0xKVVVvRjkwRklUSHYrZzk3VXdaVHRLb0tEQzhNNGNnMDI5aGJJQlRJL3dneUpGUXRSbG5TaHBveG54UkgiLCJtYWMiOiJkYWVlZmIyMWUzMzdiZmIyMzNjZmIwOTQwMGYzYWUzMzhkOTA3NjEyZmI5MjcxM2VlNmZhZDk0ZTkyYjYxODQwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJVOHoyWnRzWFZqN1N1UWt4bWphaXc9PSIsInZhbHVlIjoiaXB5Z2Q3Ukd5R0JWSWxEb2V6bm5WQ0pMREFub0plS2M1VWxjaXpjR3ZrTnp4WDRnSDJHVE42WHVYcVNhTXVOZGdGV3hMK0FaZndGVnpSUGZMakNRY0lNYXluVWtOVkxhZFFNbk1INC9nWWtNRS85dmxycHV6VkU0VTNnOFJwTXA3R1RzWlpBR1V1Y3N5Z2RPc0IrekdkNjNkdS8wYTdpekNiYks1S0JpeDJxRzRBS3RLMWViOElIVitETHFXbHl6RkJ1N0FQNndubmx4QjgvSExlN3F3RE1Zbmw3TFVxVjYxbkxST2xPVTVIbEduWkZRelFpdVV6ekdRVUU3V0craC85MmJyZU1GWnkrWVVWRHNOUXhrSE9YRzBNVUFOb0JEZm10amdwRkthdWs0OG5PMjU3N2xQT2U3alBzWXdEN3BQVG5WZGFuOGh4azlScEllMGMzcFV2THFzMDdEUVE0ajVENVlwZVNhc0NObERhdmxUN3E4T3NtWnlJSWozSnBSREpKeHFCYVZvQW85ZzdxSU9VZXZQUXZUd2hxemN5cDM1czhwMnM1dk43bjU3bjZFT0ZUN2h4bE1uQ3Y2SWppOHlydmcyTU1ORjQ5UnRhenhnQkFIczBRckRoSitKUkw2UC9nRHhBQnN5VUg1dmJ5ZFR3cnlJNStYZDl2V05xNXQiLCJtYWMiOiJiMDRjNDcyOTkzNDA2NjJhOTQ3YzdmNjI2YjkyNTVlNzJmY2NjYzZjZGUwMjUyYzQ2YzBmZGI5MTQ0YjRlNGFiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405732399\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2045471774 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zTHXXS9JeppUWUVKEDzCvqEIACaxlxrCBZnAWC85</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oiz0ShgqeVwBA0iiNz8nvALfxGFmSgvzKZeimY3R</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045471774\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1781012967 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:50:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijg0d2xuUDBCWEFjbVBkYTAraTV0VUE9PSIsInZhbHVlIjoielc3WjNueEhtakR6emd3WWFQYlcvTkZZTkRvVVhUSEpaZmVaK3dVMlpiZm1XT2tkczBwd1FqUEdlY3FqdzhoNEhHZVFIcE9EMzhhTThXRitoNUlyOXRzRmpKSFluNjhIaU1QWDhNWHp3VTRQeWFLTmRuZ3YvSkxRMFAweTB2MFNncmVNQW01cEphR29jTHNEd0NmbGxCMXV1S3FHUCtGSGsrUVZuRWZLUzNNU1VFcXE5N3RjK09oZ2dWYzZxUnVQOSt6RUUzOVkraDVENkNMdG9LSGNaT2VCNkx3d3Blc3F0M3AvU1B4M1RsWloxaUhtby9URG9LSTBrck52QUIvMTFOZFJLWkdVeU5VdzF1UVNKNXRIbGxBRUNPZzh1ZG52a3UzMTVtSC9UTEVJT3UzT0xmcHViTXo3MlBUNk5TKzl4M1RFRW40ZHhkRCtMTnhZamh0d2Zac1Z2MVJoejB1Nk1BT0kza29DRi8zcmZWVlBJNzhVR0VwS2k0d1AxRHM4NnpQcHR0NEdCRVBzYmdhcC9SMWJPL21FWmtXc1ljeUtFOFk2VWprNmNZMXYvVHIxdlZQVmRxeGtRWkhxTGdQYjA5bStWN3dsellUZ2s0QmROdDlGUHpIWUlicHIzc2FJTm1sRnNHWnRYdzRCTHZIWXRRZmVrR2RnaTA1enFldUwiLCJtYWMiOiJjMzQ4NjRjNzcyOTRkZTA0ODFhMDQwMmVjZjVjMWIyOTExMWRkNjg4ZDdjZDU5N2RlYWQ1OThmMGFjNzllMGRiIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNZQUR5U1ExQzZzZUFtL3hMSithZ2c9PSIsInZhbHVlIjoiT3dWc0hHcjZDR0dINE1VbTRWZ0VBZ09rNHJGZXFCeUdDR3VVNVZHeTIralVIMFEyanB4eDFxbzB4N0JDeVJCaVBvY25zdlZITXlSNXlRaXV5Y2ZrN1BHaVYxSGZxQThvOEc0blZrZEFqZXBoai9DWlovUFdvRDI5MlVINXdwWElUcGVaamdOSUlrWmVQRGc4Q2xtdjJHUkZ1ZnBvMlpmcDh2UDMzZ0RRcmxlVUFjZHgxcElIQmZiMk1XUGZaMzJpZjhkOGlpbWgydlVrMnhEOVJzVjVseHRyM3VrRVBvcFpuc3RBQjIvbFBlaERLY0VVYWdVMG9nbzVuZ0RQdGFkOWFCRG9oNHMwRkNoa3A4MG4zWXJrZVNPVGIrZmdZanZESGdWVzVVZVFwMnc3SkRZc0xBdm92ODVHdUNYMHJxdFovQklBTlJmVTVoQjlOTHNxNjFxbHN0K1RadTUySXc1c1JuOWd6WTNuekdIbHB1TlVZazhTN1BjWlNUZWRsSXB1QW1zUG1xeVFHWTBxSE5oRGRtOGQ4Q0k1aEd0YnpOT2RjUE9vUHdyaFd3aDc3UXJzc0tlWENvNUxwV20wc1h0QnBLSWFIWE81K3loOHJiVitqTi9ydEE3bG5xUEZyZ3hRajZ6RnJncHlaWEhtLzZTZDVkeWwzV0JYdkkydkhYdFUiLCJtYWMiOiJhNDBiNmZjN2NmMjVmNGQ3MmMwNzNiZjFjOGE3N2YwOGU2YzdjMzc4NGY1NmUzZjg1NzUxNGYxOTdkMWRjOGFhIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijg0d2xuUDBCWEFjbVBkYTAraTV0VUE9PSIsInZhbHVlIjoielc3WjNueEhtakR6emd3WWFQYlcvTkZZTkRvVVhUSEpaZmVaK3dVMlpiZm1XT2tkczBwd1FqUEdlY3FqdzhoNEhHZVFIcE9EMzhhTThXRitoNUlyOXRzRmpKSFluNjhIaU1QWDhNWHp3VTRQeWFLTmRuZ3YvSkxRMFAweTB2MFNncmVNQW01cEphR29jTHNEd0NmbGxCMXV1S3FHUCtGSGsrUVZuRWZLUzNNU1VFcXE5N3RjK09oZ2dWYzZxUnVQOSt6RUUzOVkraDVENkNMdG9LSGNaT2VCNkx3d3Blc3F0M3AvU1B4M1RsWloxaUhtby9URG9LSTBrck52QUIvMTFOZFJLWkdVeU5VdzF1UVNKNXRIbGxBRUNPZzh1ZG52a3UzMTVtSC9UTEVJT3UzT0xmcHViTXo3MlBUNk5TKzl4M1RFRW40ZHhkRCtMTnhZamh0d2Zac1Z2MVJoejB1Nk1BT0kza29DRi8zcmZWVlBJNzhVR0VwS2k0d1AxRHM4NnpQcHR0NEdCRVBzYmdhcC9SMWJPL21FWmtXc1ljeUtFOFk2VWprNmNZMXYvVHIxdlZQVmRxeGtRWkhxTGdQYjA5bStWN3dsellUZ2s0QmROdDlGUHpIWUlicHIzc2FJTm1sRnNHWnRYdzRCTHZIWXRRZmVrR2RnaTA1enFldUwiLCJtYWMiOiJjMzQ4NjRjNzcyOTRkZTA0ODFhMDQwMmVjZjVjMWIyOTExMWRkNjg4ZDdjZDU5N2RlYWQ1OThmMGFjNzllMGRiIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNZQUR5U1ExQzZzZUFtL3hMSithZ2c9PSIsInZhbHVlIjoiT3dWc0hHcjZDR0dINE1VbTRWZ0VBZ09rNHJGZXFCeUdDR3VVNVZHeTIralVIMFEyanB4eDFxbzB4N0JDeVJCaVBvY25zdlZITXlSNXlRaXV5Y2ZrN1BHaVYxSGZxQThvOEc0blZrZEFqZXBoai9DWlovUFdvRDI5MlVINXdwWElUcGVaamdOSUlrWmVQRGc4Q2xtdjJHUkZ1ZnBvMlpmcDh2UDMzZ0RRcmxlVUFjZHgxcElIQmZiMk1XUGZaMzJpZjhkOGlpbWgydlVrMnhEOVJzVjVseHRyM3VrRVBvcFpuc3RBQjIvbFBlaERLY0VVYWdVMG9nbzVuZ0RQdGFkOWFCRG9oNHMwRkNoa3A4MG4zWXJrZVNPVGIrZmdZanZESGdWVzVVZVFwMnc3SkRZc0xBdm92ODVHdUNYMHJxdFovQklBTlJmVTVoQjlOTHNxNjFxbHN0K1RadTUySXc1c1JuOWd6WTNuekdIbHB1TlVZazhTN1BjWlNUZWRsSXB1QW1zUG1xeVFHWTBxSE5oRGRtOGQ4Q0k1aEd0YnpOT2RjUE9vUHdyaFd3aDc3UXJzc0tlWENvNUxwV20wc1h0QnBLSWFIWE81K3loOHJiVitqTi9ydEE3bG5xUEZyZ3hRajZ6RnJncHlaWEhtLzZTZDVkeWwzV0JYdkkydkhYdFUiLCJtYWMiOiJhNDBiNmZjN2NmMjVmNGQ3MmMwNzNiZjFjOGE3N2YwOGU2YzdjMzc4NGY1NmUzZjg1NzUxNGYxOTdkMWRjOGFhIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781012967\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2071720906 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zTHXXS9JeppUWUVKEDzCvqEIACaxlxrCBZnAWC85</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2071720906\", {\"maxDepth\":0})</script>\n"}}