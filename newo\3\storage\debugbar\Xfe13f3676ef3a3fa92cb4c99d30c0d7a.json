{"__meta": {"id": "Xfe13f3676ef3a3fa92cb4c99d30c0d7a", "datetime": "2025-06-13 08:50:31", "utime": **********.800458, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749804630.445155, "end": **********.800493, "duration": 1.3553380966186523, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1749804630.445155, "relative_start": 0, "end": **********.566957, "relative_end": **********.566957, "duration": 1.1218020915985107, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.56698, "relative_start": 1.1218249797821045, "end": **********.800497, "relative_end": 4.0531158447265625e-06, "duration": 0.23351716995239258, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44268256, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025079999999999998, "accumulated_duration_str": "25.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.663482, "duration": 0.020059999999999998, "duration_str": "20.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.984}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.70923, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 79.984, "width_percent": 5.104}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.760957, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 85.088, "width_percent": 8.812}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7807791, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.9, "width_percent": 6.1}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1095995343 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1095995343\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1396802438 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1396802438\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-373925701 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-373925701\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1035320890 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwq%7C0%7C1960; _clsk=12sww79%7C1749804607060%7C1%7C1%7Ce.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJqa21LYUJDWTNSbXBhQlNXSmFiY1E9PSIsInZhbHVlIjoiN2huR2t3Q0JMZ0o3d3pNYlg5ZTJ5UHkyQ0VRbE1UT2xFSWxFYVJhU1grbW9SajUxQlRCeDFOMlZUTGVMOWVmQWk1MjBFN2RCNG1la1Z5U25SWDlsSWRVck9EZ2pYKzRCOCsreW1teDQyRDZSMlZMZ1Y5cUIyeVp5Rzd0TEZFMnBUbzl3QnhaZjJ6N0hNWlZaaTYxTnVuQ3F5ZGFqZTNlVkY1SjNLMXVleWduc0tyOVU1NHg5TjhQQ2U0M0RmSGxiSE9INXNPL09WaXVzc0J5azF4Wm1uUWJGSTJwUHRybnJTS0wzZ0xYb0twMTZlaHNwQTJpUzBHZWdSR2xjcHRKQ1RjaGl6bDg3OTVZRTl3ekRTeXJXYVRzMnQxVUhxcy9SSVhCYXVYNTZTSEVvVG53dFJoYmJDWmJGLzVMQmpSSDF6R2l3YmtyUU9OQW0yWktqczQyckVWa2w3aEYwS21BY0ViWklya1B0S2lQZ0dPd3h4T0MxQkJhT1lZZTRiQ2JKRHpWTWxvNjgrWnNpcG1MbkN5c2tybVlhRTBQNEJ2QmFtMU5vdXp0ajJhVWJzZ3ovN0dINkVLU0FnRzRpdTZqYlBreDNVQysvM0xkdFhHdjRSUUFGcTJpRThGT1A4eFFmeWlOaEViaG9PL2xCdW0reXF0alIrZDhRdlh0TDRUNEgiLCJtYWMiOiJiYWJmY2JkMzFiZDMxMjkzMDNhNmQwZTgwMTQxNjg3Mjk0MDY2YzBhN2JkYzExZGIzYzU0ZWI4ZjVhZjcyNmU1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNId1JoRkVaZUhiWUhKZ2tNWUw5Q1E9PSIsInZhbHVlIjoiSFFMZ3VHSzBiaEtqemEzV2xYVW1EUXN0U0QwTTJJYUF2SnRCOFAxZUlBVEhkaHRqSElORGtCMDloaG1oRmJLUWlOUVVKc2p2V3Mxb3ZVRlIyVVhGMkFRb3VEZWVxSkhUNkRXTEJtbDQvbFBiMHE3TWt6TVFLR2xmNDVuY1pzWDhoUS9xbnJwMUo0NzhSckdwZVd1bFFPVTB0VDVzanRRUDhxVFhQMlRZTFlxYmFJZ2w0c0RjaGJ3OENyajg5ZXI5VmVXRTRwbzVNR3hnRmlVZkIyMHlXelJjcEVoNGpFcG90dW5jQk1wcVV3bXhoZytsekVzai9yS0xKdVIxV3hHOEpxYXJ4UDIzQ2JyYjNWYWRHZTYvNzZkL1h6TjNuVjMzTEJXK2tZY2hoL1E0S1hRTUhuQnZtelBVbUdjYktDakFnMk5oMFdZdGY0MitBaDhPSGliTzgwQjFRTFgySDJnNXNaQTU1eUtzYzJtRzFZSGx0aDRFV1pvaTd5TEdSTWYvaHloOER4Q3ppVGV0b0psNVJkY1I2Qk9JNW9qSWpyV2pONHFENXRZSU1DZDF5U1BER04vVU1ZbXExQStsVGRZYStjMjNsTjUvcHlqaXFJc1lkTVdQbC9wNW1CVWhnSStIRnM2bGFjMnNISW5jcmlwZnVidG5Na0dsTDFJdTJQNk0iLCJtYWMiOiI4MjcxN2U3ZDBlNTk1MzFkMWRlMWFmODMxNWUzNzNiMDA0ZDFmM2MwNTI3MjliNzZiZGMzOTQxMjVhMzEwODZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035320890\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1326921365 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0uOFLLEhlaC7hLZhV6PQxR4ynv8i3FxiSJX6PrUr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1326921365\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1128073508 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 08:50:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBlL2hRaHJURENvNHdMK2xONjYxMnc9PSIsInZhbHVlIjoiNlJlZ2RQYWswZlM1MFFRb3hWZ29pNXNaK0xSenZVR08zVGtnMUlFbWNTTE9uWVRSNzFrdHN4eWRhTlltYWNETjhxcEtIQ2o1c0d3d0tWZlFLOHlWZmJJRDdHcVk4V3cvMW9NdkpJbG1BY3l4UDFtdnVGa3RwWnZsMzZaMEFWWHBRcWRoR2NPQTV1cUxybGFJQU9QVFFkYncvOVNqQlRZWVlMclA2OEpFQWdERmpVS2dzRmh0OER3TVZJSURNanFTUVJaS0tYK1lCdkRBZjhCcGpsWEc1eW1PcC9oZ3BsWVNIS2lOVkVZQ1VuMUF6WExQeFFzcHZPZzNXbzM3Nno5K3RWaHVGZWtkeFdrZE5VbHhORktRSDVvMXNtZEs3NmZLazNCWE93SWVORnJScWtJTWl0VG1NbE56eXQ5K21rZmozOERIbFF6Vkc1WEU1bG8zckgwVGJodmV3cWVQZGlvVU1IdGVzNDFKQ08rTVFoK09KNFNIeEEzVURnMm9DSDR3cGsybHEyc0VzNElNN2pIV0tUQTRRWm43RC9ab1U3MXR2MzdUKzB3NW55cTA1RnRCMnY5TUMzWjI5a25qN2p3MnF3QXh6bWV0cHUwNzY2a0RzKzBVSTZWNm0xNFptdC8wQmpZUllmZ1RvVE1TWmtNSnlrMGtvMStzYjh4ME5vR1AiLCJtYWMiOiIzYWRkYWZmNTFmYTRjOTFjYWYzY2Y2ZWM0YTI4ZjMyMWU5NTg3ZTA4NWY3MDkxYjA5Zjk4ZGYyNTBhZmYwMzhkIiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InlCSE5HV3l2b1hvclBrUUh1UlVURFE9PSIsInZhbHVlIjoiRmt0Z0tzbUVxcGhtR0QzZWVjUkx0VUVHaVdhU1N6Q2JQRjVrTDhCZlMyanJ6UTlHUHBwWDJnRWppY2FKTUQ3cGwydHZ6VTRTOFlQTTNGdkZsQTFDVEQvZ0NOaGpaWkN6MXpINTFDdnNsTHVqNEEvNkZJT2VLajl0QVJ4ajdzbGFaMXJxYTNzcGpibEpiVk12RE4yOU9USGxnRGRZU2ZBUmZheXV1b1BkdXV2bDZ0enVJZ1podlFrK1lUNlZ0VUJTOTRHU1haeGZmWjlNVHltcFk0UDhNR1lvR3FpYmVVekQ3UUl1RHpkNHpGZWNnOVBmMWVqRDR4RVhwT2s2d21LRTVtVEFsL2VuTHlSNkNkTTY3c0lnNEtPL2xsZzFwa29sV1B5UEtMdGtGdXNpYzlGVVgzczlvTXhla1hBUkFhTkd0b2Vkb2xhNnlsdUxWbE9kNUQ1cGFkN0lVSHVoT0g0eUJYU1ZCWmVJOWNjd2RHNC91YmlwVVVsbjFQaVY5c1dzZDU5Y1ZUVmQ0ZXJaMDdnMERncWJYTkdxcUVCNS94a3BVdHJRUTUxMjVpQTZURXFDYTJscEd5cFZwVW1seTNVbjFEbDNVNTlqM0dUaGxsQ0NVbzNXbHlOMUJNR2wrYTl4UzRESjdDdHUvNzlVc3NERkVxT3J5WjBqaStjWHYzdmkiLCJtYWMiOiIwN2QzNzU4OGMwNDQ2MTM2YTRmMWI0MTU4MzNkZDBjYmJiNDVjNWVjMTYxMGUyZDZkY2NmMmE3ZjY0OGVkNDM5IiwidGFnIjoiIn0%3D; expires=Fri, 13 Jun 2025 10:50:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBlL2hRaHJURENvNHdMK2xONjYxMnc9PSIsInZhbHVlIjoiNlJlZ2RQYWswZlM1MFFRb3hWZ29pNXNaK0xSenZVR08zVGtnMUlFbWNTTE9uWVRSNzFrdHN4eWRhTlltYWNETjhxcEtIQ2o1c0d3d0tWZlFLOHlWZmJJRDdHcVk4V3cvMW9NdkpJbG1BY3l4UDFtdnVGa3RwWnZsMzZaMEFWWHBRcWRoR2NPQTV1cUxybGFJQU9QVFFkYncvOVNqQlRZWVlMclA2OEpFQWdERmpVS2dzRmh0OER3TVZJSURNanFTUVJaS0tYK1lCdkRBZjhCcGpsWEc1eW1PcC9oZ3BsWVNIS2lOVkVZQ1VuMUF6WExQeFFzcHZPZzNXbzM3Nno5K3RWaHVGZWtkeFdrZE5VbHhORktRSDVvMXNtZEs3NmZLazNCWE93SWVORnJScWtJTWl0VG1NbE56eXQ5K21rZmozOERIbFF6Vkc1WEU1bG8zckgwVGJodmV3cWVQZGlvVU1IdGVzNDFKQ08rTVFoK09KNFNIeEEzVURnMm9DSDR3cGsybHEyc0VzNElNN2pIV0tUQTRRWm43RC9ab1U3MXR2MzdUKzB3NW55cTA1RnRCMnY5TUMzWjI5a25qN2p3MnF3QXh6bWV0cHUwNzY2a0RzKzBVSTZWNm0xNFptdC8wQmpZUllmZ1RvVE1TWmtNSnlrMGtvMStzYjh4ME5vR1AiLCJtYWMiOiIzYWRkYWZmNTFmYTRjOTFjYWYzY2Y2ZWM0YTI4ZjMyMWU5NTg3ZTA4NWY3MDkxYjA5Zjk4ZGYyNTBhZmYwMzhkIiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InlCSE5HV3l2b1hvclBrUUh1UlVURFE9PSIsInZhbHVlIjoiRmt0Z0tzbUVxcGhtR0QzZWVjUkx0VUVHaVdhU1N6Q2JQRjVrTDhCZlMyanJ6UTlHUHBwWDJnRWppY2FKTUQ3cGwydHZ6VTRTOFlQTTNGdkZsQTFDVEQvZ0NOaGpaWkN6MXpINTFDdnNsTHVqNEEvNkZJT2VLajl0QVJ4ajdzbGFaMXJxYTNzcGpibEpiVk12RE4yOU9USGxnRGRZU2ZBUmZheXV1b1BkdXV2bDZ0enVJZ1podlFrK1lUNlZ0VUJTOTRHU1haeGZmWjlNVHltcFk0UDhNR1lvR3FpYmVVekQ3UUl1RHpkNHpGZWNnOVBmMWVqRDR4RVhwT2s2d21LRTVtVEFsL2VuTHlSNkNkTTY3c0lnNEtPL2xsZzFwa29sV1B5UEtMdGtGdXNpYzlGVVgzczlvTXhla1hBUkFhTkd0b2Vkb2xhNnlsdUxWbE9kNUQ1cGFkN0lVSHVoT0g0eUJYU1ZCWmVJOWNjd2RHNC91YmlwVVVsbjFQaVY5c1dzZDU5Y1ZUVmQ0ZXJaMDdnMERncWJYTkdxcUVCNS94a3BVdHJRUTUxMjVpQTZURXFDYTJscEd5cFZwVW1seTNVbjFEbDNVNTlqM0dUaGxsQ0NVbzNXbHlOMUJNR2wrYTl4UzRESjdDdHUvNzlVc3NERkVxT3J5WjBqaStjWHYzdmkiLCJtYWMiOiIwN2QzNzU4OGMwNDQ2MTM2YTRmMWI0MTU4MzNkZDBjYmJiNDVjNWVjMTYxMGUyZDZkY2NmMmE3ZjY0OGVkNDM5IiwidGFnIjoiIn0%3D; expires=Fri, 13-Jun-2025 10:50:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128073508\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1576858836 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9ym5Tq2AYvwslAtvtHec4kNdOEP2MTTDDM9rJ4xX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576858836\", {\"maxDepth\":0})</script>\n"}}